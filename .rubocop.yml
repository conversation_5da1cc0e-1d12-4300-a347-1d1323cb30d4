require:
  - rubocop-rails
  - rubocop-performance
  - rubocop-rspec
  - rubocop-i18n
  - rubocop-rake

inherit_from:
  - .rubocop_todo.yml

AllCops:
  NewCops: enable
  TargetRubyVersion: 3.2
  SuggestExtensions: false
  Exclude:
    - 'bin/**/*'
    - 'db/**/*'
    - 'tmp/**/*'
    - 'vendor/**/*'
    - 'node_modules/**/*'
    - 'config/initializers/*'
    - 'lib/tasks/**/*'
  DisplayCopNames: true
  DisplayStyleGuide: true

Style/Documentation:
  Enabled: false

Style/FrozenStringLiteralComment:
  Enabled: false

Metrics/BlockLength:
  Max: 30
  Exclude:
    - 'spec/**/*'
    - 'config/environments/*'
    - 'config/routes.rb'
    - 'config/initializers/*'
    - 'app/views/admin/exam_registration_forms/export_excel.xlsx.axlsx'
    - 'lib/tasks/**/*'

Metrics/ClassLength:
  Max: 220

Metrics/MethodLength:
  Max: 50

Metrics/AbcSize:
  Max: 70

Metrics/PerceivedComplexity:
  Max: 15

Layout/LineLength:
  Max: 120
  Exclude:
    - 'config/initializers/*'

# Disable all I18n checks for the entire project
I18n/GetText/DecorateString:
  Enabled: false

I18n/RailsI18n/DecorateString:
  Enabled: false

I18n/GetText/DecorateFunctionMessage:
  Enabled: false

# Allow variable names with numbers in rake tasks and models
Naming/VariableNumber:
  Exclude:
    - 'lib/tasks/**/*'
    - 'app/models/exam_result.rb'

# Allow OpenStruct in rake tasks
Style/OpenStructUse:
  Exclude:
    - 'lib/tasks/**/*'
