# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2025-04-01 02:04:48 UTC using RuboCop version 1.69.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: TreatCommentsAsGroupSeparators, ConsiderPunctuation, Include.
# Include: **/*.gemfile, **/Gemfile, **/gems.rb
Bundler/OrderedGems:
  Exclude:
    - 'Gemfile'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
I18n/GetText/DecorateFunctionMessage:
  Exclude:
    - 'app/controllers/exam_registration_forms_controller.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect.
I18n/GetText/DecorateString:
  Exclude:
    - 'app/controllers/about_controller.rb'
    - 'app/controllers/contacts_controller.rb'
    - 'app/controllers/exam_registration_forms_controller.rb'

# Offense count: 4
I18n/RailsI18n/DecorateString:
  Exclude:
    - 'app/controllers/about_controller.rb'
    - 'app/controllers/contacts_controller.rb'
    - 'app/controllers/exam_registration_forms_controller.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Layout/EmptyLineAfterGuardClause:
  Exclude:
    - 'lib/tasks/import_master_data.rake'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EmptyLineBetweenMethodDefs, EmptyLineBetweenClassDefs, EmptyLineBetweenModuleDefs, DefLikeMacros, AllowAdjacentOneLineDefs, NumberOfEmptyLines.
Layout/EmptyLineBetweenDefs:
  Exclude:
    - 'app/controllers/admin/forms_controller.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Layout/EmptyLines:
  Exclude:
    - 'app/models/master/identity_document_type.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: empty_lines, no_empty_lines
Layout/EmptyLinesAroundBlockBody:
  Exclude:
    - 'lib/tasks/import_master_data.rake'

# Offense count: 8
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: empty_lines, empty_lines_except_namespace, empty_lines_special, no_empty_lines, beginning_only, ending_only
Layout/EmptyLinesAroundClassBody:
  Exclude:
    - 'app/controllers/search_results_controller.rb'
    - 'app/models/master/city.rb'
    - 'app/models/master/college.rb'
    - 'app/models/master/exam_center.rb'
    - 'app/models/master/identity_document_type.rb'
    - 'app/models/master/registration_reason.rb'
    - 'app/models/master/support_choice.rb'
    - 'app/models/user.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: special_inside_parentheses, consistent, align_brackets
Layout/FirstArrayElementIndentation:
  Exclude:
    - 'app/views/admin/exam_registration_forms/export_excel.xlsx.axlsx'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowMultipleStyles, EnforcedHashRocketStyle, EnforcedColonStyle, EnforcedLastArgumentHashStyle.
# SupportedHashRocketStyles: key, separator, table
# SupportedColonStyles: key, separator, table
# SupportedLastArgumentHashStyles: always_inspect, always_ignore, ignore_implicit, ignore_explicit
Layout/HashAlignment:
  Exclude:
    - 'app/models/contact.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Width, AllowedPatterns.
Layout/IndentationWidth:
  Exclude:
    - 'app/views/admin/exam_registration_forms/export_excel.xlsx.axlsx'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: aligned, indented, indented_relative_to_receiver
Layout/MultilineMethodCallIndentation:
  Exclude:
    - 'config/environments/production.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBrackets.
# SupportedStyles: space, no_space, compact
# SupportedStylesForEmptyBrackets: space, no_space
Layout/SpaceInsideArrayLiteralBrackets:
  Exclude:
    - 'config/environments/production.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: final_newline, final_blank_line
Layout/TrailingEmptyLines:
  Exclude:
    - 'app/models/master/support_choice.rb'
    - 'lib/tasks/import_master_data.rake'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: strict, consistent
Lint/SymbolConversion:
  Exclude:
    - 'app/models/exam_registration_form.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, AllowUnusedKeywordArguments, IgnoreEmptyMethods, IgnoreNotImplementedMethods, NotImplementedExceptions.
# NotImplementedExceptions: NotImplementedError
Lint/UnusedMethodArgument:
  Exclude:
    - 'app/controllers/users/sessions_controller.rb'
    - 'app/models/application_record.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect.
Lint/UselessAssignment:
  Exclude:
    - 'app/controllers/exam_registration_forms_controller.rb'

# Offense count: 8
# Configuration parameters: AllowedMethods, AllowedPatterns, CountRepeatedAttributes.
Metrics/AbcSize:
  Max: 57

# Offense count: 3
# Configuration parameters: AllowedMethods, AllowedPatterns.
Metrics/CyclomaticComplexity:
  Max: 11

# Offense count: 13
# Configuration parameters: CountComments, CountAsOne, AllowedMethods, AllowedPatterns.
Metrics/MethodLength:
  Max: 33

# Offense count: 3
# Configuration parameters: AllowedMethods, AllowedPatterns.
Metrics/PerceivedComplexity:
  Max: 11

# Offense count: 4
# Configuration parameters: EnforcedStyle, CheckMethodNames, CheckSymbols, AllowedIdentifiers, AllowedPatterns.
# SupportedStyles: snake_case, normalcase, non_integer
# AllowedIdentifiers: capture3, iso8601, rfc1123_date, rfc822, rfc2822, rfc3339, x86_64
Naming/VariableNumber:
  Exclude:
    - 'app/models/exam_result.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: NilOrEmpty, NotPresent, UnlessPresent.
Rails/Blank:
  Exclude:
    - 'app/models/exam_registration_form.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Severity, Include.
# Include: app/models/**/*.rb
Rails/EnumSyntax:
  Exclude:
    - 'app/models/exam_registration_form.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: slashes, arguments
Rails/FilePath:
  Exclude:
    - 'lib/tasks/import_master_data.rake'

# Offense count: 4
Rails/I18nLocaleTexts:
  Exclude:
    - 'app/controllers/admin/forms_controller.rb'
    - 'app/controllers/contacts_controller.rb'
    - 'app/mailers/user_mailer.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Include.
# Include: app/**/*.rb, config/**/*.rb, db/**/*.rb, lib/**/*.rb
Rails/Output:
  Exclude:
    - 'app/services/import_master_data_service.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedReceivers.
# AllowedReceivers: ActionMailer::Preview, ActiveSupport::TimeZone
Rails/RedundantActiveRecordAllMethod:
  Exclude:
    - 'app/controllers/admin/exam_registration_forms_controller.rb'

# Offense count: 4
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/ReflectionClassName:
  Exclude:
    - 'app/models/master/exam_center.rb'
    - 'app/models/master/exam_day.rb'
    - 'app/models/master/exam_session.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: strict, flexible
Rails/TimeZone:
  Exclude:
    - 'app/controllers/admin/exam_registration_forms_controller.rb'
    - 'app/controllers/admin/forms_controller.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/WhereEquals:
  Exclude:
    - 'app/controllers/admin/exam_results_controller.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, ProceduralMethods, FunctionalMethods, AllowedMethods, AllowedPatterns, AllowBracesOnProceduralOneLiners, BracesRequiredMethods.
# SupportedStyles: line_count_based, semantic, braces_for_chaining, always_braces
# ProceduralMethods: benchmark, bm, bmbm, create, each_with_object, measure, new, realtime, tap, with_object
# FunctionalMethods: let, let!, subject, watch
# AllowedMethods: lambda, proc, it
Style/BlockDelimiters:
  Exclude:
    - 'app/controllers/admin/forms_controller.rb'

# Offense count: 8
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: nested, compact
Style/ClassAndModuleChildren:
  Exclude:
    - 'app/controllers/users/confirmations_controller.rb'
    - 'app/controllers/users/omniauth_callbacks_controller.rb'
    - 'app/controllers/users/passwords_controller.rb'
    - 'app/controllers/users/registrations_controller.rb'
    - 'app/controllers/users/sessions_controller.rb'
    - 'app/controllers/users/unlocks_controller.rb'
    - 'app/helpers/admin/exam_registration_forms_helper.rb'
    - 'app/helpers/admin/forms_helper.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, EnforcedStyle.
# SupportedStyles: compact, expanded
Style/EmptyMethod:
  Exclude:
    - 'app/controllers/admin/exam_results_controller.rb'
    - 'app/controllers/home_controller.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/GlobalStdStream:
  Exclude:
    - 'config/environments/production.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: MinBodyLength, AllowConsecutiveConditionals.
Style/GuardClause:
  Exclude:
    - 'app/controllers/search_results_controller.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/IfUnlessModifier:
  Exclude:
    - 'app/controllers/search_results_controller.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: PreferredDelimiters.
Style/PercentLiteralDelimiters:
  Exclude:
    - 'config/application.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: .
# SupportedStyles: same_as_string_literals, single_quotes, double_quotes
Style/QuotedSymbols:
  EnforcedStyle: double_quotes

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantConstantBase:
  Exclude:
    - 'config/environments/production.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: SafeForConstants.
Style/RedundantFetchBlock:
  Exclude:
    - 'config/puma.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/RedundantInterpolation:
  Exclude:
    - 'app/controllers/exam_registration_forms_controller.rb'

# Offense count: 88
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, ConsistentQuotesInMultiline.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiterals:
  Enabled: false

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, no_comma
Style/TrailingCommaInHashLiteral:
  Exclude:
    - 'config/routes.rb'
