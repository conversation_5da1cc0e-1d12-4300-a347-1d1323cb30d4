# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_06_11_053235) do
  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "bank_transaction_imports", force: :cascade do |t|
    t.string "file_name", null: false
    t.integer "total_transactions", default: 0
    t.integer "processed_count", default: 0
    t.integer "matched_count", default: 0
    t.text "import_notes"
    t.datetime "imported_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["imported_at"], name: "index_bank_transaction_imports_on_imported_at"
  end

  create_table "cities", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_cities_on_name", unique: true
  end

  create_table "colleges", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_colleges_on_name", unique: true
  end

  create_table "contacts", force: :cascade do |t|
    t.string "name", null: false
    t.string "email", null: false
    t.text "message", null: false
    t.boolean "read", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "exam_centers", force: :cascade do |t|
    t.string "address", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_visible", default: true, null: false
    t.integer "position", default: 0, null: false
    t.decimal "exam_fee", precision: 10, scale: 2, default: "1600000.0", null: false
    t.index ["position"], name: "index_exam_centers_on_position"
  end

  create_table "exam_days", force: :cascade do |t|
    t.date "date", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "exam_center_id", null: false
    t.index ["exam_center_id"], name: "index_exam_days_on_exam_center_id"
  end

  create_table "exam_registration_forms", force: :cascade do |t|
    t.string "email", null: false
    t.string "phone_number", null: false
    t.string "firstname", null: false
    t.string "lastname", null: false
    t.datetime "birthday", null: false
    t.integer "gender", null: false
    t.integer "work_location_type", null: false
    t.string "company_name"
    t.string "id_document_number", null: false
    t.datetime "id_expiry_date", null: false
    t.string "id_issue_place", null: false
    t.boolean "need_specific_support", default: false
    t.string "support_choice"
    t.string "other_support_choice"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "college_name", null: false
    t.string "exam_time_range", null: false
    t.string "id_document_type", null: false
    t.string "registration_reason", null: false
    t.string "exam_center_name", null: false
    t.string "exam_day", null: false
    t.integer "payment_status", default: 0, null: false
    t.decimal "payment_amount", precision: 10, scale: 2, default: "1600000.0"
    t.string "sepay_transaction_id"
    t.datetime "payment_confirmed_at"
    t.string "sepay_payment_code"
    t.string "certificate_delivery_method", default: "pickup_at_center", null: false
    t.text "delivery_address"
    t.text "other_difficulty"
    t.text "other_registration_reason"
    t.text "detailed_contact_address"
    t.text "exam_level"
    t.string "uuid", null: false
    t.text "payment_note"
    t.index ["payment_status"], name: "index_exam_registration_forms_on_payment_status"
    t.index ["sepay_payment_code"], name: "index_exam_registration_forms_on_sepay_payment_code"
    t.index ["sepay_transaction_id"], name: "index_exam_registration_forms_on_sepay_transaction_id"
    t.index ["uuid"], name: "index_exam_registration_forms_on_uuid", unique: true
  end

  create_table "exam_results", force: :cascade do |t|
    t.string "student_name"
    t.string "identification_number"
    t.date "dob"
    t.string "gender"
    t.string "school_name"
    t.date "test_date"
    t.string "test_location"
    t.string "student_code"
    t.integer "listening_score"
    t.integer "reading_score"
    t.integer "speaking_score_1"
    t.integer "speaking_score_2"
    t.integer "total_score"
    t.string "listening_level"
    t.string "reading_level"
    t.string "speaking_level_1"
    t.string "speaking_level_2"
    t.string "overall_level"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "g_v"
    t.index ["identification_number"], name: "index_exam_results_on_identification_number"
    t.index ["student_code"], name: "index_exam_results_on_student_code"
  end

  create_table "exam_sessions", force: :cascade do |t|
    t.integer "exam_day_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "time_range", null: false
    t.integer "slot", default: 30
    t.index ["exam_day_id"], name: "index_exam_sessions_on_exam_day_id"
  end

  create_table "identity_document_types", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_identity_document_types_on_name", unique: true
  end

  create_table "master_certificate_delivery_methods", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_master_certificate_delivery_methods_on_name", unique: true
  end

  create_table "registration_reasons", force: :cascade do |t|
    t.string "description", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["description"], name: "index_registration_reasons_on_description", unique: true
  end

  create_table "support_choices", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
end
