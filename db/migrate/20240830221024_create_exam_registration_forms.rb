class CreateExamRegistrationForms < ActiveRecord::Migration[7.1]
  def change
    create_table :exam_registration_forms do |t|
      t.references :exam_session, null: false
      t.references :college, null: false
      t.references :city, null: false

      t.string :email, null: false
      t.string :phone_number, null: false
      t.string :firstname, null: false
      t.string :lastname, null: false
      t.datetime :birthday, null: false
      t.integer :gender, null: false
      t.integer :work_location_type, null: false
      t.string :company_name
      t.integer :registration_reason, null: false
      t.integer :id_document_type, null: false
      t.string :id_document_number, null: false
      t.datetime :id_expiry_date, null: false
      t.string :id_issue_place, null: false
      t.boolean :need_specific_support, default: false
      t.integer :support_choice
      t.string :other_support_choice

      t.timestamps
    end
  end
end
