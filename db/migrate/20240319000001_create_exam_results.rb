class CreateExamResults < ActiveRecord::Migration[7.0]
  def change
    create_table :exam_results do |t|
      t.string :student_name
      t.string :identification_number
      t.date :dob
      t.string :gender
      t.string :school_name
      t.date :test_date
      t.string :test_location
      t.string :student_code
      t.integer :listening_score
      t.integer :reading_score
      t.integer :speaking_score_1
      t.integer :speaking_score_2
      t.integer :total_score
      t.string :listening_level
      t.string :reading_level
      t.string :speaking_level_1
      t.string :speaking_level_2
      t.string :overall_level

      t.timestamps
    end

    add_index :exam_results, :identification_number
    add_index :exam_results, :student_code
  end
end
