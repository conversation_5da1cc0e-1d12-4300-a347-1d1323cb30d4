class AddUuidAndNoteToExamRegistrationForms < ActiveRecord::Migration[7.1]
  def change
    add_column :exam_registration_forms, :uuid, :string
    add_column :exam_registration_forms, :payment_note, :text

    # Tạo UUID cho các bản ghi hiện có
    reversible do |dir|
      dir.up do
        ExamRegistrationForm.find_each do |form|
          form.update_column(:uuid, SecureRandom.uuid)
        end

        # Sau khi tạo UUID cho tất cả bản ghi, thêm constraint not null
        change_column_null :exam_registration_forms, :uuid, false
      end
    end

    add_index :exam_registration_forms, :uuid, unique: true
  end
end
