class CreateBankTransactionImports < ActiveRecord::Migration[7.1]
  def change
    create_table :bank_transaction_imports do |t|
      t.string :file_name, null: false
      t.integer :total_transactions, default: 0
      t.integer :processed_count, default: 0
      t.integer :matched_count, default: 0
      t.text :import_notes
      t.datetime :imported_at, null: false

      t.timestamps
    end

    add_index :bank_transaction_imports, :imported_at
  end
end
