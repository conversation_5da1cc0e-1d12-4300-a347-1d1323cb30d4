class AddPaymentFieldsToExamRegistrationForms < ActiveRecord::Migration[7.1]
  def change
    add_column :exam_registration_forms, :payment_status, :integer, default: 0, null: false
    add_column :exam_registration_forms, :payment_amount, :decimal, precision: 10, scale: 2, default: 1600000.00
    add_column :exam_registration_forms, :sepay_transaction_id, :string
    add_column :exam_registration_forms, :payment_confirmed_at, :datetime
    add_column :exam_registration_forms, :sepay_payment_code, :string
    
    add_index :exam_registration_forms, :payment_status
    add_index :exam_registration_forms, :sepay_transaction_id
    add_index :exam_registration_forms, :sepay_payment_code
  end
end
