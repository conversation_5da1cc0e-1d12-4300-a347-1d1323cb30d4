class RemoveReferencesInExamRegistrationForm < ActiveRecord::Migration[7.1]
  def change
    remove_reference :exam_registration_forms, :city
    remove_reference :exam_registration_forms, :college
    remove_reference :exam_registration_forms, :exam_session

    remove_column :exam_registration_forms, :id_document_type, :integer
    remove_column :exam_registration_forms, :registration_reason, :integer

    add_column :exam_registration_forms, :city_name, :string, null: false
    add_column :exam_registration_forms, :college_name, :string, null: false
    add_column :exam_registration_forms, :exam_time_range, :string, null: false
    add_column :exam_registration_forms, :id_document_type, :string, null: false
    add_column :exam_registration_forms, :registration_reason, :string, null: false
    add_column :exam_registration_forms, :exam_center_name, :string, null: false
    add_column :exam_registration_forms, :exam_day, :string, null: false
  end
end
