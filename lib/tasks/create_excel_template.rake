namespace :exam_results do
  desc "Create Excel template for exam results import"
  task create_template: :environment do
    require 'axlsx'

    filename = "exam_results_template_#{Time.current.strftime('%Y%m%d')}.xlsx"
    filepath = Rails.root.join('tmp', filename)

    Axlsx::Package.new do |p|
      p.workbook.add_worksheet(name: "Exam Results") do |sheet|
        # Header row
        headers = [
          'Họ', 'Tên', 'CCCD/CMND', '<PERSON><PERSON><PERSON> sinh', '<PERSON><PERSON>ới tính',
          '<PERSON>r<PERSON>ờng', '<PERSON><PERSON><PERSON> thi', '<PERSON><PERSON>a điểm thi', '<PERSON><PERSON> họ<PERSON> sinh', 'G/V',
          '<PERSON><PERSON>ể<PERSON>', '<PERSON><PERSON>ể<PERSON>', 'Điểm Nói 1', 'Điểm Nói 2', 'Tổng điểm',
          '<PERSON> Nghe', '<PERSON> Đọc', 'Level Nói 1', 'Level Nói 2', 'Level tổng'
        ]

        # Add header row with styling
        sheet.add_row headers, style: p.workbook.styles.add_style(
          bg_color: "DDDDDD",
          fg_color: "000000",
          b: true,
          alignment: { horizontal: :center }
        )

        # Add sample data rows
        sample_rows = [
          [
            '<PERSON>an', 'Th<PERSON>nh <PERSON><PERSON>t', '123456789', Date.new(1999, 2, 12), '<PERSON>',
            'Tự Do', Date.new(2025, 3, 29), 'Hà Nội - 26 Đinh Núp', '12345678', '10',
            10, 10, 10, 10, 50,
            'B1', 'B1', 'B1', 'B1', 'B1'
          ],
          [
            'Nguyễn', 'Văn An', '987654321', Date.new(1998, 5, 15), 'Nam',
            'Đại học Bách Khoa', Date.new(2025, 3, 29), 'Hà Nội - 26 Đinh Núp', '87654321', '9',
            8, 9, 8, 9, 44,
            'B1', 'B1', 'B1', 'B1', 'B1'
          ],
          [
            'Trần', 'Thị Bình', '456789123', Date.new(2000, 8, 20), 'Nữ',
            'Đại học Kinh tế Quốc dân', Date.new(2025, 3, 29), 'Hồ Chí Minh - 123 Nguyễn Văn Cừ', '45678912', '8',
            9, 8, 9, 8, 44,
            'B1', 'B1', 'B1', 'B1', 'B1'
          ]
        ]

        sample_rows.each do |row|
          sheet.add_row row
        end

        # Auto-fit columns
        sheet.column_widths(*Array.new(headers.length, nil))

        # Add instructions worksheet
        p.workbook.add_worksheet(name: "Instructions") do |instruction_sheet|
          instructions = [
            ["HƯỚNG DẪN SỬ DỤNG TEMPLATE IMPORT EXAM RESULTS"],
            [""],
            ["1. CẤU TRÚC FILE:"],
            ["   - Sheet 'Exam Results' chứa dữ liệu cần import"],
            ["   - Dòng đầu tiên là header (không được thay đổi)"],
            ["   - Dữ liệu bắt đầu từ dòng 2"],
            [""],
            ["2. CÁC TRƯỜNG BẮT BUỘC:"],
            ["   - CCCD/CMND (cột C): Bắt buộc, không được trùng"],
            ["   - Họ + Tên (cột A, B): Bắt buộc"],
            ["   - Ngày sinh (cột D): Bắt buộc, format DD/MM/YYYY"],
            ["   - Ngày thi (cột G): Bắt buộc, format DD/MM/YYYY"],
            ["   - Mã học sinh (cột I): Bắt buộc"],
            ["   - G/V (cột J): Bắt buộc"],
            [""],
            ["3. FORMAT DỮ LIỆU:"],
            ["   - Giới tính: 'Nam' hoặc 'Nữ'"],
            ["   - Điểm số: Số nguyên từ 0-10"],
            ["   - Level: A1, A2, B1, B2, C1, C2"],
            ["   - Ngày tháng: DD/MM/YYYY"],
            [""],
            ["4. CÁCH IMPORT:"],
            ["   - Lưu file với tên bất kỳ (giữ format .xlsx)"],
            ["   - Chạy lệnh: rails exam_results:import_from_file[path/to/file.xlsx]"],
            ["   - Kiểm tra kết quả: rails exam_results:stats"],
            [""],
            ["5. LƯU Ý:"],
            ["   - Backup dữ liệu trước khi import"],
            ["   - Nếu CCCD/CMND đã tồn tại, record sẽ được cập nhật"],
            ["   - Kiểm tra log để xem các lỗi nếu có"],
            [""],
            ["6. LIÊN HỆ HỖ TRỢ:"],
            ["   - Email: <EMAIL>"],
            ["   - Điện thoại: 0325214191"]
          ]

          instructions.each_with_index do |instruction, index|
            if index.zero?
              # Title row
              instruction_sheet.add_row instruction, style: p.workbook.styles.add_style(
                bg_color: "4472C4",
                fg_color: "FFFFFF",
                b: true,
                sz: 14,
                alignment: { horizontal: :center }
              )
            else
              instruction_sheet.add_row instruction
            end
          end

          # Auto-fit columns for instructions
          instruction_sheet.column_widths 80
        end
      end

      p.serialize(filepath)
    end

    puts "✓ Created Excel template at #{filepath}"
    puts "Use this template to prepare your exam results data for import."
    puts "Run: rails exam_results:import_from_file[#{filepath}] to test import with sample data."
  end
end
