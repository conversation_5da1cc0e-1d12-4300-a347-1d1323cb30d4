namespace :seo do
  desc "Generate sitemap.xml"
  task generate_sitemap: :environment do
    require 'builder'

    # Set default host for URL generation
    Rails.application.routes.default_url_options[:host] = 'sea-edu.com.vn'
    Rails.application.routes.default_url_options[:protocol] = 'https'

    xml = Builder::XmlMarkup.new(indent: 2)
    xml.instruct! :xml, version: '1.0', encoding: 'UTF-8'

    xml.urlset xmlns: 'http://www.sitemaps.org/schemas/sitemap/0.9' do
      # Static pages
      urls = [
        { loc: Rails.application.routes.url_helpers.root_url, priority: 1.0, changefreq: 'daily' },
        { loc: Rails.application.routes.url_helpers.about_url, priority: 0.8, changefreq: 'monthly' },
        { loc: Rails.application.routes.url_helpers.certificate_introduction_url, priority: 0.8, changefreq: 'monthly' },
        { loc: Rails.application.routes.url_helpers.search_results_url, priority: 0.7, changefreq: 'weekly' },
        { loc: Rails.application.routes.url_helpers.exam_registration_forms_url, priority: 0.9, changefreq: 'daily' },
        { loc: Rails.application.routes.url_helpers.contact_url, priority: 0.6, changefreq: 'monthly' }
      ]

      urls.each do |url_data|
        xml.url do
          xml.loc url_data[:loc]
          xml.lastmod Date.current.strftime('%Y-%m-%d')
          xml.changefreq url_data[:changefreq]
          xml.priority url_data[:priority]
        end
      end
    end

    File.write(Rails.root.join('public', 'sitemap.xml'), xml.target!)
    puts "Sitemap generated at public/sitemap.xml"
  end

  desc "Submit sitemap to search engines"
  task submit_sitemap: :environment do
    sitemap_url = "https://sea-edu.com.vn/sitemap.xml"

    # Google
    google_url = "https://www.google.com/ping?sitemap=#{CGI.escape(sitemap_url)}"
    puts "Submitting to Google: #{google_url}"

    # Bing
    bing_url = "https://www.bing.com/ping?sitemap=#{CGI.escape(sitemap_url)}"
    puts "Submitting to Bing: #{bing_url}"

    puts "Please visit these URLs to submit your sitemap manually:"
    puts "Google: #{google_url}"
    puts "Bing: #{bing_url}"
  end

  desc "Check SEO health"
  task check_seo: :environment do
    puts "=== SEO Health Check ==="

    # Check if meta-tags gem is working
    puts "✓ Meta-tags gem installed" if defined?(MetaTags)

    # Check robots.txt
    robots_path = Rails.root.join('public', 'robots.txt')
    if File.exist?(robots_path)
      puts "✓ robots.txt exists"
      content = File.read(robots_path)
      puts "✓ Sitemap URL in robots.txt" if content.include?('sitemap.xml')
    else
      puts "✗ robots.txt missing"
    end

    # Check sitemap
    sitemap_path = Rails.root.join('public', 'sitemap.xml')
    if File.exist?(sitemap_path)
      puts "✓ sitemap.xml exists"
    else
      puts "✗ sitemap.xml missing - run 'rails seo:generate_sitemap'"
    end

    # Check favicon
    favicon_path = Rails.root.join('public', 'favicon.ico')
    puts File.exist?(favicon_path) ? "✓ favicon.ico exists" : "✗ favicon.ico missing"

    puts "\n=== Recommendations ==="
    puts "- Run 'rails seo:generate_sitemap' to create/update sitemap"
    puts "- Submit sitemap to Google Search Console"
    puts "- Monitor page speed with Google PageSpeed Insights"
    puts "- Set up Google Analytics and Search Console"
  end
end
