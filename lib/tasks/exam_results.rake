namespace :exam_results do
  desc "Import exam results from Excel file"
  task :import_from_file, [:file_path] => :environment do |_task, args|
    file_path = args[:file_path]

    unless file_path
      puts "Usage: rails exam_results:import_from_file[path/to/file.xlsx]"
      exit
    end

    unless File.exist?(file_path)
      puts "Error: File not found at #{file_path}"
      exit
    end

    puts "Starting import from #{file_path}..."

    begin
      # Tạo một file object gi<PERSON> để sử dụng với method import hiện có
      file_obj = OpenStruct.new(path: file_path)

      # Đế<PERSON> số records trước khi import
      initial_count = ExamResult.count

      # Import dữ liệu
      ExamResult.import(file_obj)

      # <PERSON><PERSON><PERSON> số records sau khi import
      final_count = ExamResult.count
      imported_count = final_count - initial_count

      puts "✓ Successfully imported #{imported_count} exam results"
      puts "Total exam results in database: #{final_count}"
    rescue StandardError => e
      puts "✗ Error during import: #{e.message}"
      puts e.backtrace.first(5).join("\n")
    end
  end

  desc "Import sample exam results data"
  task import_sample_data: :environment do
    puts "Importing sample exam results data..."

    sample_data = [
      {
        student_name: "<PERSON>an Thành Đạt",
        identification_number: "123456789",
        dob: Date.new(1999, 2, 12),
        gender: "Nam",
        school_name: "Tự Do",
        test_date: Date.new(2025, 3, 29),
        test_location: "Hà Nội - 26 Đinh Núp",
        student_code: "12345678",
        listening_score: 10,
        reading_score: 10,
        speaking_score_1: 10,
        speaking_score_2: 10,
        total_score: 50,
        listening_level: "B1",
        reading_level: "B1",
        speaking_level_1: "B1",
        speaking_level_2: "B1",
        overall_level: "B1",
        g_v: "10"
      },
      {
        student_name: "Nguyễn Văn An",
        identification_number: "987654321",
        dob: Date.new(1998, 5, 15),
        gender: "Nam",
        school_name: "Đại học Bách Khoa",
        test_date: Date.new(2025, 3, 29),
        test_location: "Hà Nội - 26 Đinh Núp",
        student_code: "87654321",
        listening_score: 8,
        reading_score: 9,
        speaking_score_1: 8,
        speaking_score_2: 9,
        total_score: 44,
        listening_level: "B1",
        reading_level: "B1",
        speaking_level_1: "B1",
        speaking_level_2: "B1",
        overall_level: "B1",
        g_v: "9"
      },
      {
        student_name: "Trần Thị Bình",
        identification_number: "456789123",
        dob: Date.new(2000, 8, 20),
        gender: "Nữ",
        school_name: "Đại học Kinh tế Quốc dân",
        test_date: Date.new(2025, 3, 29),
        test_location: "Hồ Chí Minh - 123 Nguyễn Văn Cừ",
        student_code: "45678912",
        listening_score: 9,
        reading_score: 8,
        speaking_score_1: 9,
        speaking_score_2: 8,
        total_score: 44,
        listening_level: "B1",
        reading_level: "B1",
        speaking_level_1: "B1",
        speaking_level_2: "B1",
        overall_level: "B1",
        g_v: "8"
      }
    ]

    ExamResult.count
    imported_count = 0

    sample_data.each do |data|
      # Kiểm tra xem record đã tồn tại chưa dựa trên identification_number
      existing_record = ExamResult.find_by(identification_number: data[:identification_number])

      if existing_record
        puts "Updating existing record for #{data[:student_name]} (ID: #{data[:identification_number]})"
        existing_record.update!(data)
      else
        puts "Creating new record for #{data[:student_name]} (ID: #{data[:identification_number]})"
        ExamResult.create!(data)
        imported_count += 1
      end
    end

    final_count = ExamResult.count

    puts "✓ Successfully processed #{sample_data.count} exam results"
    puts "New records created: #{imported_count}"
    puts "Total exam results in database: #{final_count}"
  end

  desc "Clear all exam results data"
  task clear_all: :environment do
    print "Are you sure you want to delete ALL exam results? This cannot be undone. (y/N): "
    input = $stdin.gets.chomp.downcase

    if %w[y yes].include?(input)
      count = ExamResult.count
      ExamResult.destroy_all
      puts "✓ Deleted #{count} exam results"
    else
      puts "Operation cancelled"
    end
  end

  desc "Show exam results statistics"
  task stats: :environment do
    puts "Exam Results Statistics:"
    puts "=" * 50

    total_count = ExamResult.count
    puts "Total records: #{total_count}"

    if total_count.positive?
      # Stats by gender
      gender_stats = ExamResult.group(:gender).count
      puts "\nBy Gender:"
      gender_stats.each { |gender, count| puts "  #{gender}: #{count}" }

      # Stats by overall level
      level_stats = ExamResult.group(:overall_level).count
      puts "\nBy Overall Level:"
      level_stats.each { |level, count| puts "  #{level}: #{count}" }

      # Stats by test location
      location_stats = ExamResult.group(:test_location).count
      puts "\nBy Test Location:"
      location_stats.each { |location, count| puts "  #{location}: #{count}" }

      # Score statistics
      avg_total_score = ExamResult.average(:total_score).to_f.round(2)
      max_total_score = ExamResult.maximum(:total_score)
      min_total_score = ExamResult.minimum(:total_score)

      puts "\nScore Statistics:"
      puts "  Average Total Score: #{avg_total_score}"
      puts "  Highest Total Score: #{max_total_score}"
      puts "  Lowest Total Score: #{min_total_score}"

      # Recent records
      recent_records = ExamResult.order(created_at: :desc).limit(5)
      puts "\nRecent Records (Last 5):"
      recent_records.each do |record|
        puts "  #{record.student_name} - #{record.identification_number} - #{record.total_score} points"
      end
    end

    puts "\n#{'=' * 50}"
  end

  desc "Export exam results to CSV"
  task export_csv: :environment do
    require 'csv'

    filename = "exam_results_export_#{Time.current.strftime('%Y%m%d_%H%M%S')}.csv"
    filepath = Rails.root.join('tmp', filename)

    CSV.open(filepath, 'w', write_headers: true, headers: [
               'ID', 'Student Name', 'Identification Number', 'Date of Birth', 'Gender',
               'School Name', 'Test Date', 'Test Location', 'Student Code', 'G/V',
               'Listening Score', 'Reading Score', 'Speaking Score 1', 'Speaking Score 2',
               'Total Score', 'Listening Level', 'Reading Level', 'Speaking Level 1',
               'Speaking Level 2', 'Overall Level', 'Created At', 'Updated At'
             ]) do |csv|
      ExamResult.find_each do |result|
        csv << [
          result.id,
          result.student_name,
          result.identification_number,
          result.dob,
          result.gender,
          result.school_name,
          result.test_date,
          result.test_location,
          result.student_code,
          result.g_v,
          result.listening_score,
          result.reading_score,
          result.speaking_score_1,
          result.speaking_score_2,
          result.total_score,
          result.listening_level,
          result.reading_level,
          result.speaking_level_1,
          result.speaking_level_2,
          result.overall_level,
          result.created_at,
          result.updated_at
        ]
      end
    end

    puts "✓ Exported #{ExamResult.count} exam results to #{filepath}"
  end
end
