namespace :exam_days do
  desc "Update exam days from 2024 to 2025"
  task update_to_2025: :environment do
    puts "Starting update exam days from 2024 to 2025..."

    # 1. Cập nhật file exam_days.yml
    puts "Updating exam_days.yml file..."
    exam_days_file = Rails.root.join("db/master_data/exam_days.yml")

    if File.exist?(exam_days_file)
      content = File.read(exam_days_file)
      updated_content = content.gsub('date: "2024-', 'date: "2025-')

      File.write(exam_days_file, updated_content)
      puts "✓ Updated exam_days.yml file"
    else
      puts "✗ exam_days.yml file not found"
    end

    # 2. Cập nhật database Master::ExamDay
    puts "Updating Master::ExamDay records in database..."
    exam_days_2024 = Master::ExamDay.where(date: Date.new(2024, 1, 1)...Date.new(2025, 1, 1))

    puts "Found #{exam_days_2024.count} exam days in 2024"

    exam_days_2024.each do |exam_day|
      old_date = exam_day.date
      new_date = Date.new(2025, old_date.month, old_date.day)

      exam_day.update!(date: new_date)
      puts "Updated exam day ID #{exam_day.id}: #{old_date} → #{new_date}"
    end

    # 3. Cập nhật ExamRegistrationForm records
    puts "Updating ExamRegistrationForm records..."

    # Tìm tất cả records có exam_day năm 2024 (format DD/MM/2024)
    exam_forms_2024 = ExamRegistrationForm.where("exam_day LIKE ?", "%/2024")

    puts "Found #{exam_forms_2024.count} exam registration forms with 2024 dates"

    exam_forms_2024.each do |form|
      old_exam_day = form.exam_day
      # Chuyển từ DD/MM/2024 thành DD/MM/2025
      new_exam_day = old_exam_day.gsub("/2024", "/2025")

      form.update!(exam_day: new_exam_day)
      puts "Updated exam registration form ID #{form.id}: #{old_exam_day} → #{new_exam_day}"
    end

    puts "✓ Successfully updated all exam days from 2024 to 2025!"
    puts "Summary:"
    puts "- Updated exam_days.yml file"
    puts "- Updated #{exam_days_2024.count} Master::ExamDay records"
    puts "- Updated #{exam_forms_2024.count} ExamRegistrationForm records"
  end

  desc "Rollback exam days from 2025 to 2024"
  task rollback_to_2024: :environment do
    puts "Starting rollback exam days from 2025 to 2024..."

    # 1. Rollback file exam_days.yml
    puts "Rolling back exam_days.yml file..."
    exam_days_file = Rails.root.join("db/master_data/exam_days.yml")

    if File.exist?(exam_days_file)
      content = File.read(exam_days_file)
      updated_content = content.gsub('date: "2025-', 'date: "2024-')

      File.write(exam_days_file, updated_content)
      puts "✓ Rolled back exam_days.yml file"
    else
      puts "✗ exam_days.yml file not found"
    end

    # 2. Rollback database Master::ExamDay
    puts "Rolling back Master::ExamDay records in database..."
    exam_days_2025 = Master::ExamDay.where(date: Date.new(2025, 1, 1)...Date.new(2026, 1, 1))

    puts "Found #{exam_days_2025.count} exam days in 2025"

    exam_days_2025.each do |exam_day|
      old_date = exam_day.date
      new_date = Date.new(2024, old_date.month, old_date.day)

      exam_day.update!(date: new_date)
      puts "Rolled back exam day ID #{exam_day.id}: #{old_date} → #{new_date}"
    end

    # 3. Rollback ExamRegistrationForm records
    puts "Rolling back ExamRegistrationForm records..."

    # Tìm tất cả records có exam_day năm 2025 (format DD/MM/2025)
    exam_forms_2025 = ExamRegistrationForm.where("exam_day LIKE ?", "%/2025")

    puts "Found #{exam_forms_2025.count} exam registration forms with 2025 dates"

    exam_forms_2025.each do |form|
      old_exam_day = form.exam_day
      # Chuyển từ DD/MM/2025 thành DD/MM/2024
      new_exam_day = old_exam_day.gsub("/2025", "/2024")

      form.update!(exam_day: new_exam_day)
      puts "Rolled back exam registration form ID #{form.id}: #{old_exam_day} → #{new_exam_day}"
    end

    puts "✓ Successfully rolled back all exam days from 2025 to 2024!"
    puts "Summary:"
    puts "- Rolled back exam_days.yml file"
    puts "- Rolled back #{exam_days_2025.count} Master::ExamDay records"
    puts "- Rolled back #{exam_forms_2025.count} ExamRegistrationForm records"
  end

  desc "Show exam days statistics by year"
  task stats: :environment do
    puts "Exam Days Statistics:"
    puts "=" * 50

    # Stats cho file YAML
    exam_days_file = Rails.root.join("db/master_data/exam_days.yml")
    if File.exist?(exam_days_file)
      content = File.read(exam_days_file)
      count_2024_yaml = content.scan('date: "2024-').count
      count_2025_yaml = content.scan('date: "2025-').count

      puts "YAML File (exam_days.yml):"
      puts "  - 2024 dates: #{count_2024_yaml}"
      puts "  - 2025 dates: #{count_2025_yaml}"
    end

    # Stats cho database Master::ExamDay
    count_2024_db = Master::ExamDay.where(date: Date.new(2024, 1, 1)...Date.new(2025, 1, 1)).count
    count_2025_db = Master::ExamDay.where(date: Date.new(2025, 1, 1)...Date.new(2026, 1, 1)).count

    puts "\nDatabase (Master::ExamDay):"
    puts "  - 2024 dates: #{count_2024_db}"
    puts "  - 2025 dates: #{count_2025_db}"

    # Stats cho ExamRegistrationForm
    count_2024_forms = ExamRegistrationForm.where("exam_day LIKE ?", "%/2024").count
    count_2025_forms = ExamRegistrationForm.where("exam_day LIKE ?", "%/2025").count

    puts "\nExam Registration Forms:"
    puts "  - 2024 dates: #{count_2024_forms}"
    puts "  - 2025 dates: #{count_2025_forms}"

    puts "\n#{'=' * 50}"
  end

  desc "Update exam days to specific year"
  task :update_to_year, %i[from_year to_year] => :environment do |_task, args|
    from_year = args[:from_year]&.to_i
    to_year = args[:to_year]&.to_i

    unless from_year && to_year
      puts "Usage: rails exam_days:update_to_year[from_year,to_year]"
      puts "Example: rails exam_days:update_to_year[2024,2025]"
      exit
    end

    puts "Starting update exam days from #{from_year} to #{to_year}..."

    # 1. Cập nhật file exam_days.yml
    puts "Updating exam_days.yml file..."
    exam_days_file = Rails.root.join("db/master_data/exam_days.yml")

    if File.exist?(exam_days_file)
      content = File.read(exam_days_file)
      updated_content = content.gsub(/date: "#{from_year}-/, "date: \"#{to_year}-")

      File.write(exam_days_file, updated_content)
      puts "✓ Updated exam_days.yml file"
    else
      puts "✗ exam_days.yml file not found"
    end

    # 2. Cập nhật database Master::ExamDay
    puts "Updating Master::ExamDay records in database..."
    exam_days_from_year = Master::ExamDay.where(date: Date.new(from_year, 1, 1)...Date.new(from_year + 1, 1, 1))

    puts "Found #{exam_days_from_year.count} exam days in #{from_year}"

    exam_days_from_year.each do |exam_day|
      old_date = exam_day.date
      new_date = Date.new(to_year, old_date.month, old_date.day)

      exam_day.update!(date: new_date)
      puts "Updated exam day ID #{exam_day.id}: #{old_date} → #{new_date}"
    end

    # 3. Cập nhật ExamRegistrationForm records
    puts "Updating ExamRegistrationForm records..."

    # Tìm tất cả records có exam_day năm from_year (format DD/MM/YYYY)
    exam_forms_from_year = ExamRegistrationForm.where("exam_day LIKE ?", "%/#{from_year}")

    puts "Found #{exam_forms_from_year.count} exam registration forms with #{from_year} dates"

    exam_forms_from_year.each do |form|
      old_exam_day = form.exam_day
      # Chuyển từ DD/MM/from_year thành DD/MM/to_year
      new_exam_day = old_exam_day.gsub("/#{from_year}", "/#{to_year}")

      form.update!(exam_day: new_exam_day)
      puts "Updated exam registration form ID #{form.id}: #{old_exam_day} → #{new_exam_day}"
    end

    puts "✓ Successfully updated all exam days from #{from_year} to #{to_year}!"
    puts "Summary:"
    puts "- Updated exam_days.yml file"
    puts "- Updated #{exam_days_from_year.count} Master::ExamDay records"
    puts "- Updated #{exam_forms_from_year.count} ExamRegistrationForm records"
  end
end
