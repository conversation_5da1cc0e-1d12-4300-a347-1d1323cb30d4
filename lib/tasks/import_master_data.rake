namespace :db do
  desc "Import all master data"
  task import_all_master_data: :environment do
    master_data_dir_path = Rails.root.join("db", "master_data")

    if Dir.exist?(master_data_dir_path)
      Dir.entries(master_data_dir_path).sort.each do |file|
        next unless file.end_with?(".yml")
        puts "processing file path: #{file}"
        file_path = master_data_dir_path.join(file)
        file_name = file.split(".").first
        model_class = "master::#{file_name.singularize.capitalize}".classify.constantize
        model_class.destroy_all

        ImportMasterDataService.new(model_class, file_path).call
      end
    else
      puts "Directory #{master_data_dir_path} does not exist."
    end
  end

  desc "Import all data including master data and exam results"
  task import_all_data: :environment do
    puts "Starting import of all data..."

    # Import master data first
    puts "\n1. Importing master data..."
    Rake::Task['db:import_all_master_data'].invoke

    # Import sample exam results
    puts "\n2. Importing sample exam results..."
    Rake::Task['exam_results:import_sample_data'].invoke

    puts "\n✓ All data import completed!"
  end

  desc "Import all data and update exam days to 2025"
  task import_all_data_2025: :environment do
    puts "Starting import of all data with 2025 exam days..."

    # Import master data first
    puts "\n1. Importing master data..."
    Rake::Task['db:import_all_master_data'].invoke

    # Update exam days to 2025
    puts "\n2. Updating exam days to 2025..."
    Rake::Task['exam_days:update_to_2025'].invoke

    # Import sample exam results
    puts "\n3. Importing sample exam results..."
    Rake::Task['exam_results:import_sample_data'].invoke

    puts "\n✓ All data import with 2025 update completed!"
  end

  desc "Setup fresh database with all data"
  task setup_fresh_db: :environment do
    puts "Setting up fresh database with all data..."

    # Drop and create database
    puts "\n1. Resetting database..."
    Rake::Task['db:drop'].invoke
    Rake::Task['db:create'].invoke
    Rake::Task['db:migrate'].invoke

    # Import all data with 2025 update
    puts "\n2. Importing all data..."
    Rake::Task['db:import_all_data_2025'].invoke

    puts "\n✓ Fresh database setup completed!"
    puts "Database is ready with:"
    puts "- All master data"
    puts "- Exam days updated to 2025"
    puts "- Sample exam results"
  end

  desc "Show database statistics"
  task show_stats: :environment do
    puts "Database Statistics:"
    puts "=" * 60

    # Master data stats
    puts "Master Data:"
    master_models = [
      Master::City,
      Master::College,
      Master::ExamCenter,
      Master::ExamDay,
      Master::ExamSession,
      Master::IdentityDocumentType,
      Master::RegistrationReason,
      Master::SupportChoice
    ]

    master_models.each do |model|
      count = model.count
      puts "  #{model.name.demodulize.pluralize}: #{count}"
    end

    # Application data stats
    puts "\nApplication Data:"
    puts "  Exam Registration Forms: #{ExamRegistrationForm.count}"
    puts "  Exam Results: #{ExamResult.count}"

    # Payment stats
    if ExamRegistrationForm.count.positive?
      payment_stats = ExamRegistrationForm.group(:payment_status).count
      puts "\nPayment Status:"
      payment_stats.each { |status, count| puts "  #{status.humanize}: #{count}" }
    end

    # Exam days stats
    puts "\nExam Days by Year:"
    Rake::Task['exam_days:stats'].invoke

    puts "\n#{'=' * 60}"
  end

end
