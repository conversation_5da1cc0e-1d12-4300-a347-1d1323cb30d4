# Cập nhật Hệ thống <PERSON>h toán và Import Giao dịch

## Tổng quan
Đã implement đầy đủ hệ thống thanh toán với UUID và import giao dịch ngân hàng theo yêu cầu.

## C<PERSON><PERSON> tính năng đã implement

### 1. UUID cho đăng ký thi
- ✅ Thêm cột `uuid` vào bảng `exam_registration_forms`
- ✅ Tự động generate UUID khi tạo đăng ký mới
- ✅ Cập nhật `generate_payment_code()` để sử dụng UUID: `VEPT [UUID]`
- ✅ Migration tự động tạo UUID cho các bản ghi hiện có

### 2. Trạng thái thanh toán và ghi chú
- ✅ Thêm cột `payment_note` để lưu thông tin chi tiết
- ✅ Các method mới:
  - `mark_as_paid!(transaction_id, amount, note)` - Đ<PERSON>h dấu đã thanh toán
  - `mark_as_insufficient!(amount, required_amount, note)` - <PERSON><PERSON> to<PERSON> không đủ
  - `mark_as_not_found!(note)` - <PERSON>h<PERSON>ng tìm thấy giao dịch

### 3. Setting phí thi cho từng trung tâm
- ✅ Thêm cột `exam_fee` vào bảng `exam_centers` (mặc định 1.6tr)
- ✅ Cập nhật admin interface tại `/admin/exam_centers` để chỉnh sửa phí
- ✅ Method `get_exam_center_fee()` để lấy phí theo trung tâm
- ✅ Tự động set `payment_amount` khi đăng ký

### 4. Hệ thống Import giao dịch ngân hàng
- ✅ Model `BankTransactionImport` để lưu lịch sử import
- ✅ Service `BankTransactionImportService` để xử lý file CSV/Excel
- ✅ Controller `/admin/bank_transaction_imports` với đầy đủ CRUD
- ✅ Giao diện upload file và xem lịch sử import

### 5. Logic kiểm tra thanh toán nâng cao
- ✅ Kiểm tra số tiền >= số tiền setting trong trung tâm thi
- ✅ Tìm kiếm giao dịch theo UUID thay vì pattern phức tạp
- ✅ Cập nhật `payment_note` với thông tin chi tiết

### 6. Hiển thị thông tin import
- ✅ Label hiển thị lần import cuối cùng
- ✅ Thống kê chi tiết: tổng GD, đã xử lý, khớp được, tỷ lệ thành công
- ✅ Ghi chú chi tiết cho từng giao dịch được xử lý

### 7. Cập nhật giao diện Admin
- ✅ Thêm menu "Import Giao dịch" trong sidebar
- ✅ Thêm cột "Phí thi" trong quản lý trung tâm thi
- ✅ Thêm cột "Ghi chú thanh toán" trong danh sách đăng ký
- ✅ Form upload file với validation và loading state

## Cấu trúc Database

### Bảng `exam_registration_forms`
```sql
-- Cột mới
uuid VARCHAR NOT NULL UNIQUE           -- UUID duy nhất cho mỗi đăng ký
payment_note TEXT                      -- Ghi chú thanh toán chi tiết
```

### Bảng `exam_centers`
```sql
-- Cột mới  
exam_fee DECIMAL(10,2) DEFAULT 1600000.00 NOT NULL  -- Phí thi cho trung tâm
```

### Bảng `bank_transaction_imports`
```sql
id BIGINT PRIMARY KEY
file_name VARCHAR NOT NULL             -- Tên file import
total_transactions INTEGER DEFAULT 0   -- Tổng số giao dịch
processed_count INTEGER DEFAULT 0      -- Số GD đã xử lý
matched_count INTEGER DEFAULT 0        -- Số GD khớp được
import_notes TEXT                      -- Ghi chú chi tiết
imported_at DATETIME NOT NULL          -- Thời gian import
created_at DATETIME
updated_at DATETIME
```

## Luồng xử lý mới

### 1. Đăng ký thi
1. User điền form đăng ký
2. Hệ thống tự động generate UUID
3. Lấy phí thi từ trung tâm được chọn
4. Tạo mã QR với format: `VEPT [UUID]`
5. Trạng thái: `pending`

### 2. Import giao dịch
1. Admin upload file CSV/Excel
2. Hệ thống parse file và tìm pattern `VEPT [UUID]`
3. Kiểm tra số tiền >= phí thi của trung tâm
4. Cập nhật trạng thái và ghi chú:
   - Đủ tiền: `paid` + "Đã chuyển khoản thành công"
   - Không đủ: `failed` + "Chuyển khoản không đủ"
   - Không tìm thấy: `failed` + "Không tìm thấy giao dịch"

### 3. SePay (chỉ tạo QR Code)
- ❌ **Đã vô hiệu hóa SePay API và webhook**
- ✅ Chỉ giữ lại tính năng tạo QR Code thanh toán
- ✅ Tất cả thanh toán được xử lý qua import giao dịch thủ công

## Format file CSV Import
```csv
Ngày,Số tiền,Nội dung,Loại
01/01/2025,1600000,"NOCN ********-1234-1234-1234-********9abc",Có
```

## API Endpoints mới
- `GET /admin/bank_transaction_imports` - Danh sách import
- `POST /admin/bank_transaction_imports` - Upload file import  
- `GET /admin/bank_transaction_imports/:id` - Chi tiết import
- `DELETE /admin/bank_transaction_imports/:id` - Xóa import

## Cách sử dụng

### 1. Cấu hình phí thi
1. Vào `/admin/exam_centers`
2. Click nút "Edit" trên trung tâm muốn thay đổi
3. Nhập phí thi mới (VND)
4. Click "Save"

### 2. Import giao dịch
1. Vào `/admin/bank_transaction_imports`
2. Click "Import file giao dịch mới"
3. Chọn file CSV/Excel
4. Click "Import"
5. Xem kết quả và chi tiết

### 3. Kiểm tra thanh toán
1. Vào `/admin/exam_registration_forms`
2. Xem cột "Trạng thái thanh toán" và "Ghi chú thanh toán"
3. Filter theo trạng thái nếu cần

## Lưu ý kỹ thuật
- UUID được generate tự động, không cần can thiệp thủ công
- File import hỗ trợ CSV và Excel (.xlsx, .xls)
- Hệ thống tự động format số tiền với dấu phẩy
- Tất cả thay đổi đều có log chi tiết trong `import_notes`
- Migration tự động tạo UUID cho dữ liệu hiện có
- **SePay API và webhook đã bị vô hiệu hóa** - chỉ tạo QR code
- **Tất cả thanh toán phải được xử lý qua import giao dịch thủ công**

## Testing
Để test hệ thống:
1. Tạo đăng ký thi mới → kiểm tra UUID được tạo
2. Thay đổi phí thi ở admin → kiểm tra QR code cập nhật
3. Upload file CSV test → kiểm tra logic matching
4. Xem chi tiết import → kiểm tra thống kê chính xác 