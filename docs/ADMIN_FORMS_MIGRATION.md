# Hướng dẫn sử dụng hệ thống quản lý dữ liệu master mới

## Tổng quan

Hệ thống quản lý dữ liệu master đã được tách từ một màn hình duy nhất (`/admin/forms`) thành nhiều màn hình riêng biệt, mỗi màn hình quản lý một loại dữ liệu cụ thể.

## C<PERSON>c thay đổi chính

### 1. Tách controller
- **Trước**: T<PERSON><PERSON> c<PERSON> chức năng trong `Admin::FormsController`
- **Sau**: Mỗi chức năng có controller riêng:
  - `Admin::CitiesController`
  - `Admin::CollegesController`
  - `Admin::IdentityDocumentTypesController`
  - `Admin::SupportChoicesController`
  - `Admin::RegistrationReasonsController`
  - `Admin::CertificateDeliveryMethodsController`
  - `Admin::ExamCentersController`
  - `Admin::ExamCenterVisibilityController`
  - `Admin::ExamDaysController`
  - `Admin::ExamSessionsController`
  - `Admin::MasterDataController` (dashboard chính)

### 2. Form submission
- **Trước**: Sử dụng AJAX (`local: false`)
- **Sau**: Sử dụng form submit thường (`local: true`)

### 3. Thông báo
- **Trước**: Thông báo thông qua AJAX response
- **Sau**: Thông báo thông qua `notice`/`alert` từ controller, hiển thị bằng Rails flash

### 4. Cấu trúc URL mới

| Chức năng | URL cũ | URL mới |
|-----------|--------|---------|
| Dashboard chính | `/admin/forms` | `/admin/master_data` |
| Quản lý thành phố | `/admin/forms#cities` | `/admin/cities` |
| Quản lý đại học/cao đẳng | `/admin/forms#colleges` | `/admin/colleges` |
| Quản lý giấy tờ tùy thân | `/admin/forms#identity-documents` | `/admin/identity_document_types` |
| Quản lý hỗ trợ đặc biệt | `/admin/forms#support-choices` | `/admin/support_choices` |
| Quản lý lý do đăng ký | `/admin/forms#registration-reasons` | `/admin/registration_reasons` |
| Quản lý phương thức nhận chứng chỉ | `/admin/forms#certificate-delivery` | `/admin/certificate_delivery_methods` |
| Quản lý trung tâm thi | `/admin/forms#exam-centers` | `/admin/exam_centers` |
| Quản lý hiển thị trung tâm | `/admin/forms#exam-center-visibility` | `/admin/exam_center_visibility` |
| Quản lý ngày thi | `/admin/forms#exam-days` | `/admin/exam_days` |
| Quản lý ca thi | `/admin/forms#exam-sessions` | `/admin/exam_sessions` |

## Hướng dẫn sử dụng

### 1. Truy cập dashboard chính
- URL: `/admin/master_data`
- Màn hình này hiển thị tất cả các chức năng quản lý dữ liệu master dưới dạng thẻ (cards)

### 2. Quản lý dữ liệu đơn giản
Các chức năng sau sử dụng giao diện đơn giản (textarea):
- Thành phố
- Đại học/Cao đẳng
- Giấy tờ tùy thân
- Hỗ trợ đặc biệt
- Lý do đăng ký thi
- Phương thức nhận chứng chỉ
- Trung tâm thi (địa chỉ)

**Cách sử dụng:**
1. Nhập mỗi item trên một dòng
2. Hệ thống sẽ tự động thêm mới hoặc xóa các item theo danh sách
3. Click "Cập nhật" để lưu thay đổi

### 3. Quản lý hiển thị trung tâm thi
- Giao diện dạng checkbox cho từng trung tâm
- Có thể chọn/bỏ chọn tất cả
- Hiển thị thông tin số ngày thi và ca thi của từng trung tâm

### 4. Quản lý ngày thi
1. Chọn trung tâm thi từ dropdown
2. Hệ thống tự động tải danh sách ngày thi hiện có
3. Chỉnh sửa danh sách theo format dd/mm/yyyy
4. Click "Cập nhật ngày thi"

### 5. Quản lý ca thi
1. Chọn trung tâm thi từ dropdown
2. Chọn ngày thi từ dropdown (được tải tự động)
3. Hệ thống tự động tải danh sách ca thi hiện có
4. Chỉnh sửa danh sách theo format HH:MM - HH:MM
5. Click "Cập nhật ca thi"

## Lợi ích của hệ thống mới

1. **Tách biệt chức năng**: Mỗi màn hình chỉ tập trung vào một loại dữ liệu
2. **Dễ bảo trì**: Code được tách thành các controller nhỏ, dễ quản lý
3. **User-friendly**: Giao diện rõ ràng, không bị rối mắt
4. **Không phụ thuộc AJAX**: Form submit thường, ít lỗi, tương thích tốt hơn
5. **Thông báo rõ ràng**: Sử dụng Rails flash, hiển thị consistent với toàn hệ thống

## Lưu ý khi phát triển

- Tất cả controllers đều kế thừa từ `Admin::ApplicationController`
- Shared partial `_simple_form.html.erb` được sử dụng cho các form đơn giản
- AJAX chỉ được sử dụng để load dữ liệu (load ngày thi, ca thi), không dùng để submit form
- Tất cả form đều sử dụng `local: true` để đảm bảo hoạt động như form HTML thường 