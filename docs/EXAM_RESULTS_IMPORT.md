# Hướng dẫn Import Exam Results

## Tổng quan
Cá<PERSON> rake tasks để quản lý và import dữ liệu kết quả thi (ExamResult) từ file Excel hoặc dữ liệu mẫu.

## Các rake tasks có sẵn

### 1. Import từ file Excel
```bash
rails exam_results:import_from_file[path/to/file.xlsx]
```

**Chức năng:** Import dữ liệu từ file Excel với format chuẩn
**Ví dụ:**
```bash
rails exam_results:import_from_file[/Users/<USER>/Downloads/exam_results.xlsx]
```

### 2. Import dữ liệu mẫu
```bash
rails exam_results:import_sample_data
```

**Chức năng:** Import 3 records mẫu để test hệ thống
**Dữ liệu mẫu bao gồm:**
- <PERSON><PERSON> (ID: 123456789)
- <PERSON><PERSON><PERSON><PERSON> (ID: 987654321) 
- Tr<PERSON><PERSON> (ID: 456789123)

### 3. <PERSON>em thống <PERSON>
```bash
rails exam_results:stats
```

**Chức năng:** Hiển thị thống kê chi tiết về exam results:
- Tổng số records
- Phân bố theo giới tính
- Phân bố theo level
- Phân bố theo địa điểm thi
- Thống kê điểm số
- 5 records gần nhất

### 4. Export ra CSV
```bash
rails exam_results:export_csv
```

**Chức năng:** Export tất cả exam results ra file CSV trong thư mục `tmp/`

### 5. Xóa tất cả dữ liệu
```bash
rails exam_results:clear_all
```

**Chức năng:** Xóa tất cả exam results (có xác nhận)

### 6. Import tất cả dữ liệu
```bash
rails db:import_all_data
```

**Chức năng:** Import cả master data và exam results sample data

## Format file Excel

Khi import từ file Excel, file cần có cấu trúc như sau:

| Cột | Tên trường | Mô tả |
|-----|------------|-------|
| A | Họ | Họ của học sinh |
| B | Tên | Tên của học sinh |
| C | CCCD/CMND | Số chứng minh thư (bắt buộc) |
| D | Ngày sinh | Ngày sinh |
| E | Giới tính | Nam/Nữ |
| F | Trường | Tên trường |
| G | Ngày thi | Ngày thi |
| H | Địa điểm thi | Địa điểm thi |
| I | Mã học sinh | Mã học sinh |
| J | G/V | Giá trị G/V |
| K | Điểm Nghe | Điểm kỹ năng Nghe |
| L | Điểm Đọc | Điểm kỹ năng Đọc |
| M | Điểm Nói 1 | Điểm Nói phần 1 |
| N | Điểm Nói 2 | Điểm Nói phần 2 |
| O | Tổng điểm | Tổng điểm |
| P | Level Nghe | Level kỹ năng Nghe |
| Q | Level Đọc | Level kỹ năng Đọc |
| R | Level Nói 1 | Level Nói phần 1 |
| S | Level Nói 2 | Level Nói phần 2 |
| T | Level tổng | Level tổng thể |

## Quy trình import

### Bước 1: Chuẩn bị file Excel
- Đảm bảo file có đúng format như bảng trên
- Dòng đầu tiên là header
- Dữ liệu bắt đầu từ dòng 2

### Bước 2: Kiểm tra trạng thái hiện tại
```bash
rails exam_results:stats
```

### Bước 3: Backup dữ liệu (khuyến nghị)
```bash
rails exam_results:export_csv
```

### Bước 4: Import dữ liệu
```bash
# Từ file Excel
rails exam_results:import_from_file[path/to/file.xlsx]

# Hoặc import dữ liệu mẫu
rails exam_results:import_sample_data
```

### Bước 5: Kiểm tra kết quả
```bash
rails exam_results:stats
```

## Ví dụ sử dụng

### Import dữ liệu mẫu để test
```bash
# Import dữ liệu mẫu
rails exam_results:import_sample_data

# Xem thống kê
rails exam_results:stats

# Export để kiểm tra
rails exam_results:export_csv
```

### Import từ file thực tế
```bash
# Import từ file Excel
rails exam_results:import_from_file[/path/to/exam_results.xlsx]

# Kiểm tra kết quả
rails exam_results:stats
```

### Làm sạch và import lại
```bash
# Xóa tất cả dữ liệu cũ
rails exam_results:clear_all

# Import dữ liệu mới
rails exam_results:import_from_file[/path/to/new_file.xlsx]
```

## Lưu ý quan trọng

### ⚠️ **Cảnh báo:**
- **Backup dữ liệu** trước khi import
- File Excel phải có đúng format
- Cột CCCD/CMND (cột C) là bắt buộc và duy nhất

### 📋 **Xử lý trùng lặp:**
- Hệ thống sẽ tự động update record nếu CCCD/CMND đã tồn tại
- Không tạo duplicate records

### 🔍 **Validation:**
- `student_name`: bắt buộc
- `identification_number`: bắt buộc, duy nhất
- `dob`: bắt buộc
- `test_date`: bắt buộc
- `student_code`: bắt buộc
- `g_v`: bắt buộc

## Troubleshooting

### Lỗi: "File not found"
```bash
# Kiểm tra đường dẫn file
ls -la /path/to/file.xlsx

# Sử dụng đường dẫn tuyệt đối
rails exam_results:import_from_file[/Users/<USER>/Downloads/file.xlsx]
```

### Lỗi: "Invalid format"
- Kiểm tra file Excel có đúng format không
- Đảm bảo dòng đầu tiên là header
- Kiểm tra các cột bắt buộc có dữ liệu

### Lỗi: "Validation failed"
```bash
# Kiểm tra dữ liệu trong Rails console
rails console

# Xem record bị lỗi
ExamResult.create!(your_data_here)
```
