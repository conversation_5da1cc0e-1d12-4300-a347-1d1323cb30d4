# Hướng dẫn sử dụng Rake Tasks

## Tổng quan
Dự án có nhiều rake tasks để quản lý dữ liệu, import/export, và cấu hình hệ thống.

## 📋 Danh sách Rake Tasks

### 🗄️ **Database & Master Data**

#### Import Master Data
```bash
# Import tất cả master data từ YAML files
rails db:import_all_master_data

# Import tất cả dữ liệu (master data + exam results)
rails db:import_all_data

# Import tất cả dữ liệu và cập nhật exam days sang 2025
rails db:import_all_data_2025

# Setup database hoàn toàn mới với tất cả dữ liệu
rails db:setup_fresh_db

# Xem thống kê database
rails db:show_stats
```

### 📅 **Exam Days Management**

#### Cập nhật năm
```bash
# Cập nhật exam days từ 2024 sang 2025
rails exam_days:update_to_2025

# Rollback từ 2025 về 2024
rails exam_days:rollback_to_2024

# Cập nhật từ năm bấ<PERSON> k<PERSON> sang năm khác
rails exam_days:update_to_year[2024,2025]

# Xem thống kê exam days theo năm
rails exam_days:stats
```

### 📊 **Exam Results Management**

#### Import & Export
```bash
# Import dữ liệu mẫu
rails exam_results:import_sample_data

# Import từ file Excel
rails exam_results:import_from_file[path/to/file.xlsx]

# Tạo template Excel
rails exam_results:create_template

# Export ra CSV
rails exam_results:export_csv

# Xem thống kê exam results
rails exam_results:stats

# Xóa tất cả exam results
rails exam_results:clear_all
```

### 💳 **Sepay Integration**

#### Test & Configuration
```bash
# Test kết nối Sepay
rails sepay:test_connection

# Kiểm tra thanh toán pending
rails sepay:check_payments
```

## 🚀 Quy trình Setup dự án mới

### Setup từ đầu (Recommended)
```bash
# 1. Setup database hoàn toàn mới
rails db:setup_fresh_db

# 2. Kiểm tra kết quả
rails db:show_stats

# 3. Test Sepay (nếu cần)
rails sepay:test_connection
```

### Setup từng bước
```bash
# 1. Tạo database và migrate
rails db:create
rails db:migrate

# 2. Import master data
rails db:import_all_master_data

# 3. Cập nhật exam days sang 2025
rails exam_days:update_to_2025

# 4. Import exam results mẫu
rails exam_results:import_sample_data

# 5. Kiểm tra kết quả
rails db:show_stats
```

## 📈 Quy trình cập nhật dữ liệu

### Cập nhật Exam Days
```bash
# 1. Kiểm tra trạng thái hiện tại
rails exam_days:stats

# 2. Backup (nếu cần)
rails exam_results:export_csv

# 3. Cập nhật sang 2025
rails exam_days:update_to_2025

# 4. Kiểm tra kết quả
rails exam_days:stats
```

### Import Exam Results từ Excel
```bash
# 1. Tạo template (lần đầu)
rails exam_results:create_template

# 2. Chuẩn bị file Excel theo template

# 3. Import dữ liệu
rails exam_results:import_from_file[/path/to/file.xlsx]

# 4. Kiểm tra kết quả
rails exam_results:stats
```

## 🔧 Troubleshooting

### Database Issues
```bash
# Reset hoàn toàn database
rails db:setup_fresh_db

# Chỉ reset master data
rails db:drop
rails db:create
rails db:migrate
rails db:import_all_master_data
```

### Exam Days Issues
```bash
# Kiểm tra trạng thái
rails exam_days:stats

# Rollback nếu cần
rails exam_days:rollback_to_2024

# Cập nhật lại
rails exam_days:update_to_2025
```

### Sepay Issues
```bash
# Test connection
rails sepay:test_connection

# Kiểm tra config
rails console
Settings.sepay.api_token
```

## 📊 Monitoring & Statistics

### Xem tổng quan hệ thống
```bash
rails db:show_stats
```

### Xem chi tiết từng phần
```bash
# Exam days
rails exam_days:stats

# Exam results
rails exam_results:stats

# Sepay payments
rails sepay:check_payments
```

## ⚠️ Lưu ý quan trọng

### Backup trước khi thay đổi
```bash
# Backup exam results
rails exam_results:export_csv

# Backup database
pg_dump your_database > backup.sql
```

### Thứ tự thực hiện
1. **Master Data** trước
2. **Exam Days** sau
3. **Exam Results** cuối cùng

### Environment
- **Development**: Có thể test thoải mái
- **Production**: Luôn backup trước khi chạy

## 🎯 Use Cases phổ biến

### Setup môi trường development
```bash
rails db:setup_fresh_db
```

### Chuẩn bị dữ liệu cho năm mới
```bash
rails exam_days:update_to_year[2024,2025]
rails db:show_stats
```

### Import kết quả thi mới
```bash
rails exam_results:create_template
# Chuẩn bị file Excel
rails exam_results:import_from_file[/path/to/results.xlsx]
rails exam_results:stats
```

### Kiểm tra hệ thống thanh toán
```bash
rails sepay:test_connection
rails sepay:check_payments
```
