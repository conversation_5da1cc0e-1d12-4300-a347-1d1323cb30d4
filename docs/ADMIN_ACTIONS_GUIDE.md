# Hướng dẫn thêm thao tác mới cho Admin

## Tổng quan
Trong màn hình admin exam_registration_forms (`/admin/exam_registration_forms`), mỗi đơn đăng ký có một dropdown menu thao tác với icon ba chấm dọc (⋮). Dropdown này được thiết kế để có thể mở rộng thêm các thao tác khác trong tương lai.

## Cấu trúc hiện tại

### 1. View Template
File: `app/views/admin/exam_registration_forms/index.html.erb`

Dropdown menu hiện tại bao gồm:
- **Xem chi tiết**: Luôn hiển thị cho tất cả đơn
- **Đánh dấu đã thanh toán**: Chỉ hiển thị cho đơn có trạng thái `pending`
- **Trạng thái hiện tại**: Hiển thị trạng thái cho đơn `paid` hoặc `failed`

### 2. Controller Actions
File: `app/controllers/admin/exam_registration_forms_controller.rb`

Hiện tại có các actions:
- `index`: Hiển thị danh sách
- `show`: Xem chi tiết
- `mark_as_paid`: Đánh dấu đã thanh toán
- `export_excel`: Xuất Excel

### 3. Routes
File: `config/routes.rb`

```ruby
resources :exam_registration_forms, only: %i[index show] do
  collection do
    get :export_excel
  end
  member do
    patch :mark_as_paid
  end
end
```

## Cách thêm thao tác mới

### Bước 1: Thêm Route
Trong `config/routes.rb`, thêm route mới vào member hoặc collection:

```ruby
member do
  patch :mark_as_paid
  patch :new_action_name  # Thêm route mới
end
```

### Bước 2: Thêm Action vào Controller
Trong `app/controllers/admin/exam_registration_forms_controller.rb`:

```ruby
def new_action_name
  # Logic xử lý
  if condition_check
    @exam_registration_form.do_something!
    redirect_to admin_exam_registration_forms_path, notice: t('.success')
  else
    redirect_to admin_exam_registration_forms_path, alert: t('.error')
  end
end

private

def set_exam_registration_form
  @exam_registration_form = ExamRegistrationForm.find(params[:id])
end
```

Nhớ thêm action mới vào `before_action`:
```ruby
before_action :set_exam_registration_form, only: %i[show mark_as_paid new_action_name]
```

### Bước 3: Thêm Method vào Model (nếu cần)
Trong `app/models/exam_registration_form.rb`:

```ruby
def do_something!
  update!(
    # Các field cần cập nhật
    field_name: new_value,
    updated_at: Time.current
  )
end
```

### Bước 4: Thêm vào Dropdown Menu
Trong `app/views/admin/exam_registration_forms/index.html.erb`, thêm item mới vào dropdown:

```erb
<ul class="dropdown-menu dropdown-menu-end">
  <!-- Existing items -->
  <li>
    <%= link_to admin_exam_registration_form_path(e), class: "dropdown-item" do %>
      <i class="fas fa-eye text-primary me-2"></i>
      Xem chi tiết
    <% end %>
  </li>
  
  <!-- Thêm item mới -->
  <% if condition_for_new_action %>
    <li><hr class="dropdown-divider"></li>
    <li>
      <%= link_to new_action_name_admin_exam_registration_form_path(e), 
                  class: "dropdown-item",
                  data: { 
                    turbo_method: :patch,  # hoặc method khác
                    confirm: "Xác nhận thực hiện thao tác này?" 
                  } do %>
        <i class="fas fa-icon-name text-color me-2"></i>
        Tên thao tác
      <% end %>
    </li>
  <% end %>
</ul>
```

### Bước 5: Thêm I18n Messages
Trong `config/locales/vi.yml`:

```yaml
admin:
  exam_registration_forms:
    new_action_name:
      success: "Thực hiện thao tác thành công!"
      error: "Có lỗi xảy ra khi thực hiện thao tác!"
```

## Ví dụ thực tế: Thêm thao tác "Hủy đơn"

### 1. Route
```ruby
member do
  patch :mark_as_paid
  patch :cancel_registration
end
```

### 2. Controller
```ruby
def cancel_registration
  if @exam_registration_form.can_be_cancelled?
    @exam_registration_form.cancel_by_admin!
    redirect_to admin_exam_registration_forms_path, notice: t('.success')
  else
    redirect_to admin_exam_registration_forms_path, alert: t('.error')
  end
end
```

### 3. Model
```ruby
def can_be_cancelled?
  payment_pending? || payment_failed?
end

def cancel_by_admin!
  update!(
    payment_status: :failed,
    payment_note: "Admin hủy đơn đăng ký",
    cancelled_at: Time.current
  )
end
```

### 4. View
```erb
<% if e.can_be_cancelled? %>
  <li>
    <%= link_to cancel_registration_admin_exam_registration_form_path(e), 
                class: "dropdown-item text-danger",
                data: { 
                  turbo_method: :patch,
                  confirm: "Bạn có chắc chắn muốn hủy đơn đăng ký này không?" 
                } do %>
      <i class="fas fa-times-circle text-danger me-2"></i>
      Hủy đơn đăng ký
    <% end %>
  </li>
<% end %>
```

### 5. I18n
```yaml
admin:
  exam_registration_forms:
    cancel_registration:
      success: "Đã hủy đơn đăng ký thành công!"
      error: "Không thể hủy đơn đăng ký này!"
```

## Lưu ý quan trọng

1. **Bảo mật**: Luôn kiểm tra quyền và điều kiện trước khi thực hiện thao tác
2. **Validation**: Kiểm tra trạng thái đơn trước khi cho phép thao tác
3. **User Experience**: Sử dụng confirmation dialog cho các thao tác quan trọng
4. **Icons**: Sử dụng FontAwesome icons phù hợp với từng thao tác
5. **Colors**: Sử dụng màu sắc phù hợp (success, danger, warning, info)
6. **I18n**: Luôn sử dụng lazy lookup cho messages

## CSS Classes có sẵn

- `text-success`: Màu xanh lá (cho thao tác tích cực)
- `text-danger`: Màu đỏ (cho thao tác nguy hiểm)
- `text-warning`: Màu vàng (cho thao tác cảnh báo)
- `text-primary`: Màu xanh dương (cho thao tác chính)
- `text-muted`: Màu xám (cho thông tin)

## FontAwesome Icons thường dùng

- `fa-check-circle`: Xác nhận, hoàn thành
- `fa-times-circle`: Hủy, từ chối
- `fa-edit`: Chỉnh sửa
- `fa-trash`: Xóa
- `fa-eye`: Xem
- `fa-download`: Tải xuống
- `fa-envelope`: Email
- `fa-phone`: Điện thoại
- `fa-print`: In ấn 