# Hướng dẫn SEO cho dự án canhbuom-v2

## Tổng quan
Dự án đã được tối ưu hóa SEO với các tính năng sau:

### ✅ Đã triển khai:

#### 1. Meta Tags động
- **Gem**: `meta-tags` đã được cài đặt
- **Helper**: `Seo<PERSON>elper` với các phương thức hỗ trợ SEO
- **Tính năng**:
  - Title, description, keywords động cho từng trang
  - Open Graph tags cho social media
  - Twitter Card tags
  - Canonical URLs

#### 2. Structured Data (JSON-LD)
- **Organization Schema**: Thông tin công ty SEA Education
- **Course Schema**: Thông tin khóa học NOCN
- **Local Business Schema**: Thông tin doanh nghiệp địa phương
- **Event Schema**: Thông tin sự kiện thi
- **Breadcrumb Schema**: Điều hướng breadcrumb
- **FAQ Schema**: Sẵn sàng cho trang FAQ

#### 3. Sitemap XML
- **Route**: `/sitemap.xml`
- **Controller**: `SitemapsController`
- **Rake task**: `rails seo:generate_sitemap`
- **Tự động**: Bao gồm tất cả trang chính

#### 4. Robots.txt
- **Cấu hình**: Cho phép crawl các trang chính
- **Chặn**: Admin pages và user authentication
- **Sitemap**: Link đến sitemap.xml

#### 5. SEO cho từng trang
- **Trang chủ**: Tối ưu cho từ khóa chính
- **Giới thiệu**: Thông tin về SEA Education và NOCN
- **Đăng ký thi**: Tối ưu cho conversion
- **Tra cứu kết quả**: Tối ưu cho user intent
- **Liên hệ**: Local SEO

## Cách sử dụng

### 1. Cập nhật Meta Tags cho trang mới
```ruby
# Trong controller
def your_action
  set_meta_tags_for_page(
    title: 'Tiêu đề trang',
    description: 'Mô tả trang',
    keywords: 'từ khóa, liên quan, trang'
  )
end
```

### 2. Generate Sitemap
```bash
# Tạo sitemap mới
rails seo:generate_sitemap

# Kiểm tra SEO health
rails seo:check_seo

# Submit sitemap (manual)
rails seo:submit_sitemap
```

### 3. Thêm Structured Data
```erb
<!-- Trong view -->
<script type="application/ld+json">
  <%= structured_data_course %>
</script>
```

## Rake Tasks

### `rails seo:generate_sitemap`
- Tạo file `public/sitemap.xml`
- Bao gồm tất cả trang chính
- Cập nhật lastmod với ngày hiện tại

### `rails seo:check_seo`
- Kiểm tra tình trạng SEO
- Xác nhận các file cần thiết
- Đưa ra khuyến nghị

### `rails seo:submit_sitemap`
- Hiển thị URL để submit sitemap
- Google Search Console
- Bing Webmaster Tools

## Cấu hình quan trọng

### Meta Tags Configuration
File: `config/initializers/meta_tags.rb`
- Title limit: 70 characters
- Description limit: 300 characters
- Keywords limit: 255 characters

### Default URL Options
Cần cấu hình trong production:
```ruby
# config/environments/production.rb
config.action_mailer.default_url_options = { 
  host: 'sea-edu.com.vn', 
  protocol: 'https' 
}
```

## Từ khóa chính đã tối ưu

### Primary Keywords:
- chứng chỉ tiếng anh NOCN
- thi tiếng anh NOCN
- SEA Education
- đăng ký thi NOCN

### Long-tail Keywords:
- đăng ký thi chứng chỉ tiếng anh NOCN online
- tra cứu kết quả thi NOCN
- trung tâm thi NOCN uy tín
- chứng chỉ tiếng anh quốc tế CEFR

## Bước tiếp theo

### 1. Google Search Console
- Xác thực domain sea-edu.com.vn
- Submit sitemap.xml
- Monitor search performance

### 2. Google Analytics
- Cài đặt GA4
- Theo dõi conversion goals
- Monitor user behavior

### 3. Page Speed Optimization
- Optimize images
- Enable compression
- Minimize CSS/JS

### 4. Content Marketing
- Blog section cho SEO content
- FAQ page với structured data
- Landing pages cho specific keywords

### 5. Local SEO
- Google My Business
- Local citations
- Customer reviews

## Monitoring & Maintenance

### Weekly:
- Check search rankings
- Monitor sitemap status
- Review Google Search Console

### Monthly:
- Update meta descriptions
- Generate new sitemap
- Analyze SEO performance

### Quarterly:
- Keyword research update
- Competitor analysis
- Technical SEO audit

## Liên hệ
Để hỗ trợ thêm về SEO, vui lòng liên hệ team development.
