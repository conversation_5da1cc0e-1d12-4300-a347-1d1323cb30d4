#!/bin/bash

# Deploy script for production environment
# Usage: ./deploy.sh

set -e  # Exit on any error

echo "🚀 Starting production deployment..."

# Pull latest changes from git
echo "📥 Pulling latest changes..."
git pull

# Run database migrations
echo "🔄 Running database migrations..."
RAILS_ENV=production bundle exec rails db:migrate

# Precompile assets
echo "🎨 Precompiling assets..."
RAILS_ENV=production rails assets:precompile

# Restart puma service
echo "🔄 Restarting puma service..."
sudo systemctl restart puma_canhbuom

echo "✅ Production deployment completed successfully!" 