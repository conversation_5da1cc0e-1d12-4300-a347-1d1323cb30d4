<!DOCTYPE html>
<html lang="vi">
<head>
  <title>Lỗi hệ thống - SEA EDUCATION</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta charset="utf-8">
  <link rel="icon" href="/favicon.ico" type="image/x-icon">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Roboto', sans-serif;
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
      overflow-x: hidden;
    }

    .error-container {
      text-align: center;
      padding: 2rem;
      max-width: 600px;
      width: 90%;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      position: relative;
      overflow: hidden;
    }

    .error-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #ff6b6b, #ee5a24);
    }

    .error-number {
      font-size: 8rem;
      font-weight: 700;
      color: #ff6b6b;
      margin-bottom: 1rem;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
    }

    .error-title {
      font-size: 2rem;
      font-weight: 500;
      color: #333;
      margin-bottom: 1rem;
    }

    .error-message {
      font-size: 1.1rem;
      color: #666;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .error-icon {
      font-size: 4rem;
      color: #ff6b6b;
      margin-bottom: 1rem;
      animation: shake 3s ease-in-out infinite;
    }

    @keyframes shake {
      0%, 100% {
        transform: translateX(0);
      }
      10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
      }
      20%, 40%, 60%, 80% {
        transform: translateX(5px);
      }
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 2rem;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 50px;
      font-size: 1rem;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.3s ease;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background: linear-gradient(45deg, #2490dc, #1e7bb8);
      color: white;
      box-shadow: 0 4px 15px rgba(36, 144, 220, 0.3);
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(36, 144, 220, 0.4);
    }

    .btn-secondary {
      background: transparent;
      color: #ff6b6b;
      border: 2px solid #ff6b6b;
    }

    .btn-secondary:hover {
      background: #ff6b6b;
      color: white;
      transform: translateY(-2px);
    }

    .btn-refresh {
      background: linear-gradient(45deg, #00b894, #00a085);
      color: white;
      box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
    }

    .btn-refresh:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 184, 148, 0.4);
    }

    .company-info {
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid #eee;
      color: #666;
      font-size: 0.9rem;
    }

    .company-logo {
      font-size: 1.5rem;
      font-weight: 700;
      color: #2490dc;
      margin-bottom: 0.5rem;
    }

    .support-info {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 1rem;
      margin: 1rem 0;
      border-left: 4px solid #ff6b6b;
    }

    .support-info h3 {
      color: #ff6b6b;
      margin-bottom: 0.5rem;
      font-size: 1.1rem;
    }

    .floating-shapes {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: -1;
    }

    .shape {
      position: absolute;
      opacity: 0.1;
      animation: float-shapes 6s ease-in-out infinite;
    }

    .shape:nth-child(1) {
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    .shape:nth-child(2) {
      top: 20%;
      right: 10%;
      animation-delay: 2s;
    }

    .shape:nth-child(3) {
      bottom: 10%;
      left: 20%;
      animation-delay: 4s;
    }

    @keyframes float-shapes {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    @media (max-width: 768px) {
      .error-number {
        font-size: 6rem;
      }

      .error-title {
        font-size: 1.5rem;
      }

      .error-message {
        font-size: 1rem;
      }

      .action-buttons {
        flex-direction: column;
        align-items: center;
      }

      .btn {
        width: 100%;
        max-width: 250px;
      }
    }

    @media (max-width: 480px) {
      .error-container {
        padding: 1.5rem;
        margin: 1rem;
      }

      .error-number {
        font-size: 4rem;
      }

      .error-title {
        font-size: 1.2rem;
      }
    }
  </style>
</head>

<body>
  <div class="floating-shapes">
    <div class="shape">
      <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ff6b6b;"></i>
    </div>
    <div class="shape">
      <i class="fas fa-tools" style="font-size: 2.5rem; color: #ff6b6b;"></i>
    </div>
    <div class="shape">
      <i class="fas fa-server" style="font-size: 3.5rem; color: #ff6b6b;"></i>
    </div>
  </div>

  <div class="error-container">
    <div class="error-icon">
      <i class="fas fa-exclamation-triangle"></i>
    </div>
    
    <div class="error-number">500</div>
    
    <h1 class="error-title">Lỗi hệ thống</h1>
    
    <p class="error-message">
      Xin lỗi, đã xảy ra lỗi không mong muốn trên hệ thống của chúng tôi. 
      Chúng tôi đang khắc phục sự cố này và sẽ hoạt động trở lại sớm nhất có thể.
    </p>

    <div class="support-info">
      <h3><i class="fas fa-info-circle"></i> Thông tin hỗ trợ</h3>
      <p>Nếu sự cố tiếp tục xảy ra, vui lòng liên hệ với chúng tôi để được hỗ trợ kịp thời.</p>
    </div>

    <div class="action-buttons">
      <a href="/" class="btn btn-primary">
        <i class="fas fa-home"></i>
        Về trang chủ
      </a>
      <a href="javascript:location.reload()" class="btn btn-refresh">
        <i class="fas fa-sync-alt"></i>
        Thử lại
      </a>
      <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i>
        Quay lại
      </a>
    </div>

    <div class="company-info">
      <div class="company-logo">SEA EDUCATION</div>
      <p>Công ty TNHH Sea Education</p>
      <p>Hotline: (+84) 0886681666 | Email: <EMAIL></p>
      <p style="margin-top: 0.5rem; font-size: 0.8rem; color: #999;">
        Mã lỗi: 500 - Thời gian: <span id="error-time"></span>
      </p>
    </div>
  </div>

  <script>
    // Display current time
    document.addEventListener('DOMContentLoaded', function() {
      const now = new Date();
      const timeString = now.toLocaleString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      document.getElementById('error-time').textContent = timeString;

      // Add click effect to buttons
      const buttons = document.querySelectorAll('.btn');
      buttons.forEach(button => {
        button.addEventListener('click', function(e) {
          const ripple = document.createElement('span');
          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);
          const x = e.clientX - rect.left - size / 2;
          const y = e.clientY - rect.top - size / 2;
          
          ripple.style.width = ripple.style.height = size + 'px';
          ripple.style.left = x + 'px';
          ripple.style.top = y + 'px';
          ripple.classList.add('ripple');
          
          this.appendChild(ripple);
          
          setTimeout(() => {
            ripple.remove();
          }, 600);
        });
      });

      // Auto refresh after 30 seconds (optional)
      setTimeout(() => {
        const refreshBtn = document.querySelector('.btn-refresh');
        if (refreshBtn) {
          refreshBtn.style.animation = 'pulse 1s infinite';
          refreshBtn.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Tự động thử lại...';
          setTimeout(() => {
            location.reload();
          }, 3000);
        }
      }, 30000);
    });
  </script>

  <style>
    .ripple {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.6);
      transform: scale(0);
      animation: ripple-animation 0.6s linear;
      pointer-events: none;
    }

    @keyframes ripple-animation {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
  </style>
</body>
</html>
