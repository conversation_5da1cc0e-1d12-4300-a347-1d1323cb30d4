<!DOCTYPE html>
<html lang="vi">
<head>
  <title>Trang không tìm thấy - SEA EDUCATION</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta charset="utf-8">
  <link rel="icon" href="/favicon.ico" type="image/x-icon">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Roboto', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
      overflow-x: hidden;
    }

    .error-container {
      text-align: center;
      padding: 2rem;
      max-width: 600px;
      width: 90%;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      position: relative;
      overflow: hidden;
    }

    .error-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #2490dc, #1e7bb8);
    }

    .error-number {
      font-size: 8rem;
      font-weight: 700;
      color: #2490dc;
      margin-bottom: 1rem;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
      animation: bounce 2s infinite;
    }

    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-10px);
      }
      60% {
        transform: translateY(-5px);
      }
    }

    .error-title {
      font-size: 2rem;
      font-weight: 500;
      color: #333;
      margin-bottom: 1rem;
    }

    .error-message {
      font-size: 1.1rem;
      color: #666;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .error-icon {
      font-size: 4rem;
      color: #2490dc;
      margin-bottom: 1rem;
      animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 2rem;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 50px;
      font-size: 1rem;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.3s ease;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background: linear-gradient(45deg, #2490dc, #1e7bb8);
      color: white;
      box-shadow: 0 4px 15px rgba(36, 144, 220, 0.3);
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(36, 144, 220, 0.4);
    }

    .btn-secondary {
      background: transparent;
      color: #2490dc;
      border: 2px solid #2490dc;
    }

    .btn-secondary:hover {
      background: #2490dc;
      color: white;
      transform: translateY(-2px);
    }

    .company-info {
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid #eee;
      color: #666;
      font-size: 0.9rem;
    }

    .company-logo {
      font-size: 1.5rem;
      font-weight: 700;
      color: #2490dc;
      margin-bottom: 0.5rem;
    }

    .floating-shapes {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: -1;
    }

    .shape {
      position: absolute;
      opacity: 0.1;
      animation: float-shapes 6s ease-in-out infinite;
    }

    .shape:nth-child(1) {
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    .shape:nth-child(2) {
      top: 20%;
      right: 10%;
      animation-delay: 2s;
    }

    .shape:nth-child(3) {
      bottom: 10%;
      left: 20%;
      animation-delay: 4s;
    }

    @keyframes float-shapes {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    @media (max-width: 768px) {
      .error-number {
        font-size: 6rem;
      }

      .error-title {
        font-size: 1.5rem;
      }

      .error-message {
        font-size: 1rem;
      }

      .action-buttons {
        flex-direction: column;
        align-items: center;
      }

      .btn {
        width: 100%;
        max-width: 250px;
      }
    }

    @media (max-width: 480px) {
      .error-container {
        padding: 1.5rem;
        margin: 1rem;
      }

      .error-number {
        font-size: 4rem;
      }

      .error-title {
        font-size: 1.2rem;
      }
    }
  </style>
</head>

<body>
  <div class="floating-shapes">
    <div class="shape">
      <i class="fas fa-graduation-cap" style="font-size: 3rem; color: #2490dc;"></i>
    </div>
    <div class="shape">
      <i class="fas fa-book" style="font-size: 2.5rem; color: #2490dc;"></i>
    </div>
    <div class="shape">
      <i class="fas fa-globe" style="font-size: 3.5rem; color: #2490dc;"></i>
    </div>
  </div>

  <div class="error-container">
    <div class="error-icon">
      <i class="fas fa-search"></i>
    </div>
    
    <div class="error-number">404</div>
    
    <h1 class="error-title">Trang không tìm thấy</h1>
    
    <p class="error-message">
      Xin lỗi, trang bạn đang tìm kiếm không tồn tại hoặc đã được di chuyển. 
      Có thể bạn đã nhập sai địa chỉ hoặc liên kết đã hết hạn.
    </p>

    <div class="action-buttons">
      <a href="/" class="btn btn-primary">
        <i class="fas fa-home"></i>
        Về trang chủ
      </a>
      <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i>
        Quay lại
      </a>
    </div>

    <div class="company-info">
      <div class="company-logo">SEA EDUCATION</div>
      <p>Công ty TNHH Sea Education</p>
      <p>Hotline: (+84) 0886681666 | Email: <EMAIL></p>
    </div>
  </div>

  <script>
    // Add some interactive effects
    document.addEventListener('DOMContentLoaded', function() {
      // Add click effect to buttons
      const buttons = document.querySelectorAll('.btn');
      buttons.forEach(button => {
        button.addEventListener('click', function(e) {
          const ripple = document.createElement('span');
          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);
          const x = e.clientX - rect.left - size / 2;
          const y = e.clientY - rect.top - size / 2;
          
          ripple.style.width = ripple.style.height = size + 'px';
          ripple.style.left = x + 'px';
          ripple.style.top = y + 'px';
          ripple.classList.add('ripple');
          
          this.appendChild(ripple);
          
          setTimeout(() => {
            ripple.remove();
          }, 600);
        });
      });
    });
  </script>

  <style>
    .ripple {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.6);
      transform: scale(0);
      animation: ripple-animation 0.6s linear;
      pointer-events: none;
    }

    @keyframes ripple-animation {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
  </style>
</body>
</html>
