require 'test_helper'

class SepayServiceTest < ActiveSupport::TestCase
  def setup
    @sepay_service = SepayService.new
    @exam_form = ExamRegistrationForm.new(
      firstname: "Test",
      lastname: "User",
      phone_number: "**********",
      exam_day: "01/01/2025",
      exam_time_range: "Sáng (8:00 - 11:00)",
      payment_amount: 1_600_000,
      uuid: SecureRandom.uuid
    )
  end

  test "should generate correct QR URL format" do
    payment_info = @sepay_service.generate_payment_qr(@exam_form)

    assert_not_nil payment_info[:qr_url]
    assert_includes payment_info[:qr_url], "https://qr.sepay.vn/img?"
    assert_includes payment_info[:qr_url], "acc=105%20800%206666"
    assert_includes payment_info[:qr_url], "bank=Vietcombank"
    assert_includes payment_info[:qr_url], "amount=1600000"
    assert_includes payment_info[:qr_url], "des=NOCN"
  end

  test "should generate payment code with correct format" do
    payment_info = @sepay_service.generate_payment_qr(@exam_form)

    assert_not_nil payment_info[:payment_code]
    assert_includes payment_info[:payment_code], "NOCN"
    # NOCN format sử dụng UUID thay vì thông tin chi tiết
  end

  test "should return bank info" do
    payment_info = @sepay_service.generate_payment_qr(@exam_form)

    assert_not_nil payment_info[:bank_info]
    assert_equal "105 800 6666", payment_info[:bank_info][:account_number]
    assert_equal "CÔNG TY TNHH SEA EDUCATION TRAINING", payment_info[:bank_info][:account_name]
    assert_includes payment_info[:bank_info][:bank_name], "Vietcombank"
  end
end
