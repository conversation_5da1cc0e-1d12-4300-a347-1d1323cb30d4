name: Rubocop

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  rubocop:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.2.2'
          bundler-cache: true
          
      - name: Install dependencies
        run: bundle install
        
      - name: <PERSON><PERSON> Rubocop
        uses: actions/cache@v4
        with:
          path: |
            .rubocop_cache
            .rubocop_todo.yml
          key: ${{ runner.os }}-rubocop-${{ hashFiles('**/Gemfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-rubocop-
            
      - name: Run Rubocop
        run: bundle exec rubocop --parallel --cache true 