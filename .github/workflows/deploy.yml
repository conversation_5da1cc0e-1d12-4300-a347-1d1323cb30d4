name: Deploy to Production

on:
  push:
    branches: [ main ]  # hoặc tên branch bạ<PERSON> muốn deploy

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to server
        uses: appleboy/ssh-action@master
        with:
          host: ***************
          username: root
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /var/www/canhbuom-v2
            git pull origin main
            docker-compose down
            docker-compose up -d --build
            docker-compose exec -T web bundle exec rails assets:precompile
            docker-compose exec -T web bundle exec rails db:migrate
            sudo systemctl restart nginx 