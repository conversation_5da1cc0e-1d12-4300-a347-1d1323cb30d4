<div class="contact-section">
  <h2 class="section-title"><PERSON><PERSON><PERSON> h<PERSON> <PERSON><PERSON> ý</h2>

  <div class="contact-info">
    <div class="contact-item">
      <i class="fas fa-envelope"></i>
      <span><EMAIL></span>
    </div>
    <div class="contact-item">
      <i class="fas fa-phone"></i>
      <span>+84 886681666</span>
    </div>
    <div class="contact-item">
      <i class="fas fa-map-marker-alt"></i>
      <span>26 <PERSON><PERSON>, Ph<PERSON><PERSON><PERSON> Trung Hò<PERSON>, <PERSON>u<PERSON><PERSON>, Tp <PERSON>à Nội</span>
    </div>
  </div>

  <div class="contact-form">
    <h3>Biểu mẫu Liên hệ - Góp ý</h3>
    <p class="form-description">M<PERSON><PERSON> dành cho thí sinh có câu hỏi, thắc mắc sẽ điền và gửi:</p>

    <div id="contact-message" class="alert" style="display: none;"></div>

    <%= form_with(model: Contact.new, local: false, class: "form", id: "contact-form",
                data: { controller: "contact-form", action: "submit->contact-form#submit" }) do |f| %>
      <div class="form-group">
        <%= f.label :name, "Tên <span class='text-danger'>(*)</span>".html_safe %>
        <%= f.text_field :name, class: "form-control #{'is-invalid' if @contact&.errors&.include?(:name)}", placeholder: "Nhập tên của bạn", required: true %>
        <div class="invalid-feedback" data-contact-form-target="nameError"></div>
      </div>

      <div class="form-group">
        <%= f.label :email, "Email <span class='text-danger'>(*)</span>".html_safe %>
        <%= f.email_field :email, class: "form-control #{'is-invalid' if @contact&.errors&.include?(:email)}", placeholder: "Nhập email của bạn", required: true %>
        <div class="invalid-feedback" data-contact-form-target="emailError"></div>
      </div>

      <div class="form-group">
        <%= f.label :message, "Nội dung tin nhắn <span class='text-danger'>(*)</span>".html_safe %>
        <%= f.text_area :message, class: "form-control #{'is-invalid' if @contact&.errors&.include?(:message)}", rows: 5, placeholder: "Nhập nội dung tin nhắn của bạn", required: true %>
        <div class="invalid-feedback" data-contact-form-target="messageError"></div>
      </div>

      <div class="form-group">
        <%= f.submit "Gửi tin nhắn", class: "btn btn-primary", data: { disable_with: "Đang gửi..." } %>
      </div>
    <% end %>
  </div>
</div>

<!-- Local Business Structured Data -->
<script type="application/ld+json">
  <%= structured_data_local_business %>
</script>
