<div class="login-container">
  <div class="login-box">
    <div class="welcome-section">
      <h1 class="welcome-title">Ch<PERSON>o mừng đến với</h1>
      <h2 class="admin-title">Trang Quản Trị</h2>
      <p class="welcome-subtitle">Vui lòng đăng nhập để tiếp tục</p>
    </div>
    
    <%= form_for(resource, as: resource_name, url: session_path(resource_name), html: { class: 'login-form' }) do |f| %>
      <div class="form-group">
        <%= f.label :email, class: 'form-label' %>
        <%= f.email_field :email, autofocus: true, autocomplete: "email", class: 'form-input' %>
      </div>

      <div class="form-group">
        <%= f.label :password, "Mật khẩu", class: 'form-label' %>
        <%= f.password_field :password, autocomplete: "current-password", class: 'form-input' %>
      </div>

      <% if devise_mapping.rememberable? %>
        <div class="form-group remember-me">
          <%= f.check_box :remember_me, class: 'form-checkbox' %>
          <%= f.label :remember_me, "Ghi nhớ đăng nhập", class: 'checkbox-label' %>
        </div>
      <% end %>

      <div class="form-group">
        <%= f.submit "Đăng nhập", class: 'login-button' %>
      </div>
    <% end %>
  </div>
</div>

<style>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.login-box {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
}

.login-title {
  text-align: center;
  color: #2c3e50;
  font-size: 28px;
  margin-bottom: 30px;
  font-weight: 600;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  color: #34495e;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-checkbox {
  width: 18px;
  height: 18px;
}

.checkbox-label {
  color: #666;
  font-size: 14px;
}

.login-button {
  width: 100%;
  padding: 12px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-button:hover {
  background: #2980b9;
}

.welcome-section {
  text-align: center;
  margin-bottom: 35px;
  padding-bottom: 25px;
  border-bottom: 1px solid #eee;
  animation: fadeInDown 0.5s ease-out;
}

.welcome-title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 400;
  margin-bottom: 10px;
}

.admin-title {
  color: #2980b9;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.welcome-subtitle {
  color: #7f8c8d;
  font-size: 16px;
  margin-bottom: 0;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .login-box {
    padding: 20px;
  }
  
  .login-title {
    font-size: 24px;
  }
  
  .admin-title {
    font-size: 26px;
  }
  
  .welcome-title {
    font-size: 18px;
  }
  
  .welcome-subtitle {
    font-size: 14px;
  }
}
</style>
