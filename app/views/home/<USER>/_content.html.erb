<div class="schedule-content">
  <div class="city-tabs">
    <% @exam_centers.each_with_index do |center, index| %>
      <button class="city-tab <%= 'active' if index == 0 %>"
              data-city="<%= center.id %>"
              onclick="showCitySchedule(<%= center.id %>)">
        <%= center.address.split(' - ').first %>
      </button>
    <% end %>
  </div>

  <% @exam_centers.each_with_index do |center, index| %>
    <div class="city-schedule <%= 'active' if index == 0 %>" id="city-<%= center.id %>">
      <div class="schedule-info">
        <div class="company-info">
          <div class="company-name"><%= Settings.company.name %></div>
          <div class="location-pin">📍 <%= center.address %></div>
        </div>

        <div class="exam-info">
          <div class="exam-dates-section">
            <div class="section-title">Ng<PERSON>y thi</div>
            <% if center.exam_days.any? %>
              <%
                grouped_dates = center.exam_days.order(:date).group_by { |day| day.date.strftime('%m/%Y') }
              %>
              <% grouped_dates.each do |month_year, days| %>
                <div class="date-group">
                  Tháng <%= month_year %>: <%= days.map { |day| day.date.strftime('%d') }.join(', ') %>
                </div>
              <% end %>
            <% else %>
              <div>Chưa có lịch thi</div>
            <% end %>
          </div>

          <div class="exam-type-section">
            <div class="section-title">Ca thi</div>
            <div class="exam-type">
              Sáng: 8h - 10h<br>
              Chiều: 13h30 - 15h30<br>
              Tối: 18h - 20h
            </div>
          </div>
        </div>
      </div>

      <div class="registration-info">
        <div class="deadline">Hạn đăng ký: trước ngày thi <span class="highlight">14 ngày</span></div>
        <%# <div class="fee">Lệ phí thi: 1.600.000 VNĐ (Một triệu sáu trăm nghìn đồng).</div> %>
        <%= link_to dang_ky_thi_lua_chon_path, class: "btn btn-danger btn-detail", style: "border-radius: 48px;" do %>
          <span>ĐĂNG KÝ DỰ THI</span>
        <% end %>
      </div>
    </div>
  <% end %>

  <% if @exam_centers.empty? %>
    <div class="empty-state text-center py-5">
      <div class="mb-3">
        <i class="fas fa-calendar-times fa-3x text-muted"></i>
      </div>
      <h5 class="text-muted">Chưa có thông tin lịch thi</h5>
      <p class="text-muted mb-4">Hiện tại chưa có trung tâm thi nào được mở đăng ký.</p>
      <%= link_to contact_path, class: "btn btn-outline-primary" do %>
        <i class="fas fa-phone me-2"></i>Liên hệ để biết thêm thông tin
      <% end %>
    </div>
  <% end %>
</div>

<script>
function showCitySchedule(cityId) {
  document.querySelectorAll('.city-schedule').forEach(function(el) {
    el.classList.remove('active');
  });

  document.querySelectorAll('.city-tab').forEach(function(el) {
    el.classList.remove('active');
  });

  document.getElementById('city-' + cityId).classList.add('active');
  document.querySelector('[data-city="' + cityId + '"]').classList.add('active');
}
</script>
