<div class="container-wrapper">
  <div class="container-lg">
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0">
              <i class="fas fa-file-import me-2"></i>Chi tiết Import: <%= @import.file_name %>
            </h4>
            <%= link_to admin_bank_transaction_imports_path, class: "btn btn-secondary" do %>
              <i class="fas fa-arrow-left me-1"></i>Quay lại danh sách
            <% end %>
          </div>
          <div class="card-body">
            <!-- Thông tin tổng quan -->
            <div class="row mb-4">
              <div class="col-md-3">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <i class="fas fa-file-csv fa-2x text-primary mb-2"></i>
                    <h5 class="card-title">Tổng giao dịch</h5>
                    <h3 class="text-primary"><%= @import.total_transactions %></h3>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <i class="fas fa-cogs fa-2x text-info mb-2"></i>
                    <h5 class="card-title">Đã xử lý</h5>
                    <h3 class="text-info"><%= @import.processed_count %></h3>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h5 class="card-title">Khớp được</h5>
                    <h3 class="text-success"><%= @import.matched_count %></h3>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <i class="fas fa-percentage fa-2x text-warning mb-2"></i>
                    <h5 class="card-title">Tỷ lệ thành công</h5>
                    <h3 class="<%= @import.success_rate >= 80 ? 'text-success' : @import.success_rate >= 50 ? 'text-warning' : 'text-danger' %>">
                      <%= @import.success_rate %>%
                    </h3>
                  </div>
                </div>
              </div>
            </div>

            <!-- Thông tin chi tiết -->
            <div class="card mb-4">
              <div class="card-header">
                <h6 class="mb-0">
                  <i class="fas fa-info-circle me-1"></i>Thông tin Import
                </h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <table class="table table-borderless">
                      <tr>
                        <td><strong>Tên file:</strong></td>
                        <td><%= @import.file_name %></td>
                      </tr>
                      <tr>
                        <td><strong>Thời gian import:</strong></td>
                        <td><%= @import.imported_at.strftime("%d/%m/%Y %H:%M:%S") %></td>
                      </tr>
                      <tr>
                        <td><strong>Tổng giao dịch:</strong></td>
                        <td><%= @import.total_transactions %></td>
                      </tr>
                    </table>
                  </div>
                  <div class="col-md-6">
                    <table class="table table-borderless">
                      <tr>
                        <td><strong>Đã xử lý:</strong></td>
                        <td><%= @import.processed_count %></td>
                      </tr>
                      <tr>
                        <td><strong>Khớp được:</strong></td>
                        <td><%= @import.matched_count %></td>
                      </tr>
                      <tr>
                        <td><strong>Tỷ lệ thành công:</strong></td>
                        <td>
                          <span class="badge <%= @import.success_rate >= 80 ? 'bg-success' : @import.success_rate >= 50 ? 'bg-warning' : 'bg-danger' %>">
                            <%= @import.success_rate %>%
                          </span>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            <!-- Ghi chú import -->
            <% if @import.import_notes.present? %>
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">
                    <i class="fas fa-sticky-note me-1"></i>Chi tiết xử lý
                  </h6>
                </div>
                <div class="card-body">
                  <div class="import-notes">
                    <% @import.import_notes.split("\n").each do |note| %>
                      <div class="note-item mb-2">
                        <% if note.start_with?('✓') %>
                          <i class="fas fa-check-circle text-success me-2"></i>
                          <span class="text-success"><%= note[1..-1].strip %></span>
                        <% elsif note.start_with?('⚠') %>
                          <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                          <span class="text-warning"><%= note[1..-1].strip %></span>
                        <% elsif note.start_with?('✗') %>
                          <i class="fas fa-times-circle text-danger me-2"></i>
                          <span class="text-danger"><%= note[1..-1].strip %></span>
                        <% else %>
                          <i class="fas fa-info-circle text-info me-2"></i>
                          <span class="text-info"><%= note %></span>
                        <% end %>
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .container-wrapper {
    background-color: #F8F8F8;
    padding: 20px 0;
    min-height: 100vh;
  }

  .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }

  .import-notes {
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
  }

  .note-item {
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #f8f9fa;
    border-left: 3px solid #dee2e6;
  }

  .note-item:has(.text-success) {
    background-color: #d1e7dd;
    border-left-color: #198754;
  }

  .note-item:has(.text-warning) {
    background-color: #fff3cd;
    border-left-color: #ffc107;
  }

  .note-item:has(.text-danger) {
    background-color: #f8d7da;
    border-left-color: #dc3545;
  }

  .note-item:has(.text-info) {
    background-color: #d1ecf1;
    border-left-color: #0dcaf0;
  }
</style> 