<div class="container-wrapper">
  <div class="container-lg">
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0">
              <i class="fas fa-file-import me-2"></i>Quản lý Import Giao dịch Ngân hàng
            </h4>
            <%= link_to admin_root_path, class: "btn btn-secondary" do %>
              <i class="fas fa-arrow-left me-1"></i>Quay lại Dashboard
            <% end %>
          </div>
          <div class="card-body">
            <!-- Thông tin import cuối cùng -->
            <% if @latest_import %>
              <div class="alert alert-info mb-4">
                <div class="row">
                  <div class="col-md-8">
                    <i class="fas fa-info-circle me-2"></i>
                    <% if @latest_import %>
                      <strong>Import cuối cùng:</strong> <%= @latest_import.file_name %> 
                      (<%= @latest_import.imported_at.strftime("%d/%m/%Y %H:%M") %>)
                      - <%= @latest_import.import_summary %>
                    <% else %>
                      <strong>Chưa có import nào</strong>
                    <% end %>
                  </div>
                  <div class="col-md-4 text-end">
                    <%= link_to admin_bank_transaction_import_path(@latest_import), class: "btn btn-sm btn-outline-primary" do %>
                      <i class="fas fa-eye me-1"></i>Xem chi tiết
                    <% end %>
                  </div>
                </div>
              </div>
            <% else %>
              <div class="alert alert-warning mb-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Chưa có lần import nào được thực hiện.
              </div>
            <% end %>

            <!-- Form upload file -->
            <div class="card border-success mb-4">
              <div class="card-header bg-light">
                <h6 class="mb-0 text-success">
                  <i class="fas fa-upload me-1"></i>Import file giao dịch mới
                </h6>
              </div>
              <div class="card-body">
                <%= form_with url: admin_bank_transaction_imports_path, method: :post, id: 'import-form', local: false, multipart: true do |form| %>
                  <div class="row">
                    <div class="col-md-8">
                      <%= form.file_field :transaction_file, class: 'form-control', accept: '.csv,.xlsx,.xls', required: true %>
                      <small class="text-muted">Chấp nhận file CSV, Excel (.xlsx, .xls)</small>
                    </div>
                    <div class="col-md-4">
                      <button type="submit" class="btn btn-success w-100" id="import-btn">
                        <i class="fas fa-upload me-1"></i>Import
                      </button>
                    </div>
                  </div>
                <% end %>
                <div class="mt-3">
                  <div class="alert alert-info">
                    <strong>Lưu ý:</strong>
                    <ul class="mb-0 mt-2">
                      <li>File CSV cần có các cột: Ngày, Số tiền, Nội dung, Loại</li>
                      <li>Hệ thống sẽ tự động tìm UUID trong nội dung giao dịch (format: VEPT [UUID])</li>
                      <li>Chỉ xử lý các giao dịch có tiền vào (Có/In)</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <!-- Danh sách lịch sử import -->
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0">
                  <i class="fas fa-history me-1"></i>Lịch sử Import
                </h6>
              </div>
              <div class="card-body">
                <% if @imports.any? %>
                  <div class="table-responsive">
                    <table class="table table-hover">
                      <thead>
                        <tr>
                          <th>Tên file</th>
                          <th class="text-center">Thời gian</th>
                          <th class="text-center">Tổng GD</th>
                          <th class="text-center">Đã xử lý</th>
                          <th class="text-center">Khớp được</th>
                          <th class="text-center">Tỷ lệ thành công</th>
                          <th class="text-center">Thao tác</th>
                        </tr>
                      </thead>
                      <tbody>
                        <% @imports.each do |import| %>
                          <tr>
                            <td>
                              <i class="fas fa-file-csv me-2 text-muted"></i>
                              <%= import.file_name %>
                            </td>
                            <td class="text-center">
                              <%= import.imported_at.strftime("%d/%m/%Y %H:%M") %>
                            </td>
                            <td class="text-center">
                              <span class="badge bg-secondary"><%= import.total_transactions %></span>
                            </td>
                            <td class="text-center">
                              <span class="badge bg-info"><%= import.processed_count %></span>
                            </td>
                            <td class="text-center">
                              <span class="badge bg-success"><%= import.matched_count %></span>
                            </td>
                            <td class="text-center">
                              <% if import.success_rate >= 80 %>
                                <span class="badge bg-success"><%= import.success_rate %>%</span>
                              <% elsif import.success_rate >= 50 %>
                                <span class="badge bg-warning"><%= import.success_rate %>%</span>
                              <% else %>
                                <span class="badge bg-danger"><%= import.success_rate %>%</span>
                              <% end %>
                            </td>
                            <td class="text-center">
                              <div class="btn-group btn-group-sm">
                                <%= link_to admin_bank_transaction_import_path(import), class: "btn btn-outline-primary" do %>
                                  <i class="fas fa-eye"></i>
                                <% end %>
                                <%= link_to admin_bank_transaction_import_path(import), method: :delete, 
                                    class: "btn btn-outline-danger", 
                                    data: { confirm: "Bạn có chắc chắn muốn xóa bản ghi import này?" } do %>
                                  <i class="fas fa-trash"></i>
                                <% end %>
                              </div>
                            </td>
                          </tr>
                        <% end %>
                      </tbody>
                    </table>
                  </div>
                <% else %>
                  <div class="text-center py-5 text-muted">
                    <i class="fas fa-file-import fa-3x mb-3"></i>
                    <h5>Chưa có lịch sử import nào</h5>
                    <p>Upload file giao dịch để bắt đầu</p>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Toast notifications -->
<div class="position-fixed top-0 end-0 p-3" style="z-index: 1050">
  <div id="notification-toast" class="toast" role="alert">
    <div class="toast-header">
      <strong class="me-auto">Thông báo</strong>
      <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
    </div>
    <div class="toast-body"></div>
  </div>
</div>

<!-- Loading modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body text-center py-4">
        <div class="spinner-border text-primary mb-3" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <h5>Đang xử lý import...</h5>
        <p class="text-muted mb-0">Vui lòng đợi trong giây lát</p>
      </div>
    </div>
  </div>
</div>

<style>
  .container-wrapper {
    background-color: #F8F8F8;
    padding: 20px 0;
    min-height: 100vh;
  }

  .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }

  .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const importForm = document.getElementById('import-form');
  const importBtn = document.getElementById('import-btn');
  const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));

  if (importForm) {
    importForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      const fileInput = this.querySelector('input[type="file"]');
      if (!fileInput.files.length) {
        showNotification('Vui lòng chọn file để upload', 'error');
        return;
      }

      // Show loading modal
      loadingModal.show();
      importBtn.disabled = true;

      const formData = new FormData(this);
      
      fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        loadingModal.hide();
        importBtn.disabled = false;
        
        if (data.status === 'success') {
          showNotification(data.message, 'success');
          setTimeout(() => {
            location.reload();
          }, 2000);
        } else {
          showNotification(data.message, 'error');
        }
      })
      .catch(error => {
        loadingModal.hide();
        importBtn.disabled = false;
        showNotification('Có lỗi xảy ra khi xử lý file', 'error');
        console.error('Error:', error);
      });
    });
  }

  function showNotification(message, type) {
    const toast = document.getElementById('notification-toast');
    const body = toast.querySelector('.toast-body');

    body.textContent = message;
    toast.classList.remove('text-bg-success', 'text-bg-danger');
    toast.classList.add(type === 'success' ? 'text-bg-success' : 'text-bg-danger');

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
  }
});
</script> 