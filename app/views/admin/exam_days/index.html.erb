<div class="container-wrapper">
  <div class="container-lg">
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title mb-0">
              <i class="fas fa-calendar-alt me-2"></i>Qu<PERSON>n lý Ngày thi
            </h4>
          </div>
          <div class="card-body">
            <%= form_with url: admin_exam_days_path, method: :patch, local: true do |form| %>
                            <div class="form-group mb-3">
                <%= form.label :exam_center_id, "Chọn trung tâm thi:", class: "form-label" %>
                <%= form.select :exam_center_id, options_for_select([['Chọn trung tâm thi', '']] + @exam_centers.map { |center| [center.address, center.id] }, params[:exam_center_id]), 
                    {}, { class: "form-control", id: "exam-center-select" } %>
              </div>

              <div class="form-group mb-3">
                <label class="form-label">Danh sách ngày thi:</label>
                <div id="exam-days-container" class="border rounded p-3" style="min-height: 200px; background-color: #f8f9fa;">
                  <div class="text-center text-muted py-4" id="empty-state-select">
                    <i class="fas fa-calendar-alt fa-3x mb-2"></i>
                    <p>Chọn trung tâm thi để xem danh sách ngày thi</p>
                  </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-3">
                  <button type="button" class="btn btn-outline-success" id="add-exam-day-btn" disabled>
                    <i class="fas fa-plus me-2"></i>Thêm ngày thi
                  </button>
                  <div class="form-text">
                    <i class="fas fa-info-circle me-1"></i>
                    Nhập ngày thi theo định dạng dd/mm/yyyy
                  </div>
                </div>
              </div>

              <div class="text-end mt-3">
                <%= link_to "Quay lại", admin_master_data_path, class: "btn btn-secondary me-2" %>
                <%= form.submit 'Cập nhật ngày thi', class: 'btn btn-primary', disabled: true, id: 'update-button' %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const examCenterSelect = document.getElementById('exam-center-select');
  const examDaysContainer = document.getElementById('exam-days-container');
  const emptyStateSelect = document.getElementById('empty-state-select');
  const addExamDayBtn = document.getElementById('add-exam-day-btn');
  const updateButton = document.getElementById('update-button');

  let examDays = [];
  let nextId = 1;
  let hasSelectedCenter = false;
  
  // Get params from URL
  const urlParams = new URLSearchParams(window.location.search);
  const preselectedCenterId = urlParams.get('exam_center_id');

  // Helper function to make AJAX requests
  function makeRequest(url, options = {}) {
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    };

    return fetch(url, { ...defaultOptions, ...options })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      });
  }

  // Convert date formats
  function dateToInput(dateStr) {
    if (!dateStr) return '';
    // Convert dd/mm/yyyy to yyyy-mm-dd for HTML5 date input
    const [day, month, year] = dateStr.split('/');
    if (day && month && year) {
      return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    }
    return '';
  }

  function inputToDate(inputStr) {
    if (!inputStr) return '';
    // Convert yyyy-mm-dd to dd/mm/yyyy for display
    const [year, month, day] = inputStr.split('-');
    if (year && month && day) {
      return `${day}/${month}/${year}`;
    }
    return '';
  }

  // Create exam day item HTML
  function createExamDayItem(date, isNew = false) {
    const id = `exam-day-${nextId++}`;
    const inputValue = dateToInput(date);
    const displayValue = isNew ? '' : date;

    return `
      <div class="exam-day-item mb-2 p-3 border rounded bg-white" data-id="${id}">
        <div class="row align-items-center">
          <div class="col-md-8">
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-calendar-day"></i>
              </span>
              <input type="date"
                     class="form-control exam-day-input"
                     value="${inputValue}"
                     min="2024-01-01"
                     max="2030-12-31">
              <div class="input-group-text date-display">
                ${displayValue || 'Chọn ngày'}
              </div>
            </div>
          </div>
          <div class="col-md-4 text-end">
            <button type="button" class="btn btn-outline-danger btn-sm remove-exam-day-btn">
              <i class="fas fa-trash me-1"></i>Xóa
            </button>
          </div>
        </div>
      </div>
    `;
  }

    // Create empty state HTML
  function createEmptyState() {
    if (!hasSelectedCenter) {
      return `
        <div class="text-center text-muted py-4">
          <i class="fas fa-calendar-alt fa-3x mb-2"></i>
          <p>Chọn trung tâm thi để xem danh sách ngày thi</p>
        </div>
      `;
    } else {
      return `
        <div class="text-center text-muted py-4">
          <i class="fas fa-info-circle fa-3x mb-2"></i>
          <p>Không có ngày thi nào</p>
          <small class="text-muted">Bấm "Thêm ngày thi" để thêm ngày thi mới</small>
        </div>
      `;
    }
  }

  // Render exam days
  function renderExamDays() {
    if (examDays.length === 0) {
      examDaysContainer.innerHTML = createEmptyState();
    } else {
      examDaysContainer.innerHTML = examDays.map(date => createExamDayItem(date)).join('');
    }
    
    // Update submit button and hidden inputs
    updateSubmitState();
  }

  // Update submit button state and create hidden inputs
  function updateSubmitState() {
    const hasData = examDays.length > 0;
    updateButton.disabled = !hasData;

    // Remove existing hidden inputs
    document.querySelectorAll('input[name="exam_days"]').forEach(input => input.remove());

    // Create hidden inputs for form submission
    if (hasData) {
      const form = document.querySelector('form');
      const textarea = document.createElement('textarea');
      textarea.name = 'exam_days';
      textarea.style.display = 'none';
      textarea.value = examDays.join('\n');
      form.appendChild(textarea);
    }
  }



  // Event: Exam center change
  examCenterSelect.addEventListener('change', function() {
    const selectedCenterId = this.value;

    if (selectedCenterId) {
      const params = new URLSearchParams({ exam_center_id: selectedCenterId });
      const url = '<%= load_by_center_admin_exam_days_path %>?' + params.toString();

      makeRequest(url)
        .then(data => {
          examDays = data.map(item => item.date.toString());
          addExamDayBtn.disabled = false;
          hasSelectedCenter = true;
          renderExamDays();
        })
        .catch(() => {
          showMessage('Có lỗi xảy ra khi tải danh sách ngày thi!', 'error');
          hasSelectedCenter = true;
          renderExamDays();
        });
    } else {
      examDays = [];
      addExamDayBtn.disabled = true;
      hasSelectedCenter = false;
      renderExamDays();
    }
  });

  // Event: Add exam day
  addExamDayBtn.addEventListener('click', function() {
    examDays.push('');
    renderExamDays();

    // Focus on the new input
    const newItems = examDaysContainer.querySelectorAll('.exam-day-item');
    const lastInput = newItems[newItems.length - 1]?.querySelector('.exam-day-input');
    if (lastInput) lastInput.focus();
  });

  // Event delegation for dynamic elements
  examDaysContainer.addEventListener('click', function(e) {
    if (e.target.closest('.remove-exam-day-btn')) {
      const item = e.target.closest('.exam-day-item');
      const index = Array.from(examDaysContainer.children).indexOf(item);
      examDays.splice(index, 1);
      renderExamDays();
    }
  });

  // Event delegation for date input changes
  examDaysContainer.addEventListener('change', function(e) {
    if (e.target.classList.contains('exam-day-input')) {
      const input = e.target;
      const item = input.closest('.exam-day-item');
      const index = Array.from(examDaysContainer.children).indexOf(item);
      const dateDisplay = item.querySelector('.date-display');

      if (input.value) {
        const displayDate = inputToDate(input.value);
        examDays[index] = displayDate;
        dateDisplay.textContent = displayDate;
        dateDisplay.classList.remove('text-muted');
        input.classList.remove('is-invalid');
      } else {
        examDays[index] = '';
        dateDisplay.textContent = 'Chọn ngày';
        dateDisplay.classList.add('text-muted');
      }

      updateSubmitState();
    }
  });

  function showMessage(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const iconClass = type === 'success' ? 'check-circle' : 'exclamation-circle';
    const alertHtml = `
      <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
        <i class="fas fa-${iconClass} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    `;

    const alertContainer = document.querySelector('.alert-container');
    if (alertContainer) {
      alertContainer.innerHTML = alertHtml;

      // Auto dismiss after 3 seconds
      setTimeout(() => {
        const alert = alertContainer.querySelector('.alert');
        if (alert) {
          const bsAlert = new bootstrap.Alert(alert);
          bsAlert.close();
        }
      }, 3000);
    }
  }
  
  // Auto-load when page loads with preselected values
  if (preselectedCenterId) {
    examCenterSelect.value = preselectedCenterId;
    examCenterSelect.dispatchEvent(new Event('change'));
  }
});
</script>

<style>
  .container-wrapper {
    background-color: #F8F8F8;
    padding: 20px 0;
    min-height: 100vh;
  }

  .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }

  .card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
  }

  .form-control {
    border-radius: 0.375rem;
  }

  .form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }

  .form-control:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
  }

  .exam-day-item {
    transition: all 0.3s ease;
    animation: fadeIn 0.3s ease-in;
  }

  .exam-day-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  .exam-day-input.is-invalid {
    border-color: #dc3545;
  }

  .remove-exam-day-btn:hover {
    transform: scale(1.05);
  }

  #exam-days-container {
    max-height: 400px;
    overflow-y: auto;
  }

  .date-display {
    min-width: 120px;
    font-weight: 500;
    background-color: #f8f9fa !important;
    border-left: 1px solid #dee2e6 !important;
  }

  .date-display.text-muted {
    color: #6c757d !important;
    font-style: italic;
  }

  .exam-day-input[type="date"] {
    position: relative;
  }

  .exam-day-input[type="date"]::-webkit-calendar-picker-indicator {
    cursor: pointer;
    border-radius: 4px;
    margin-right: 2px;
    opacity: 0.6;
    filter: invert(0.5);
  }

  .exam-day-input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
    background-color: #e9ecef;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
</style>

<div class="alert-container mt-3"></div>
