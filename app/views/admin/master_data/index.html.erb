<div class="container-wrapper">
  <div class="container-lg">
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title mb-0">
              <i class="fas fa-cogs me-2"></i><PERSON><PERSON><PERSON><PERSON> lý Dữ liệu Master
            </h4>
          </div>
          <div class="card-body">
            <div class="alert alert-info mb-4">
              <i class="fas fa-info-circle me-2"></i>
              Chọn một trong các chức năng bên dưới để quản lý dữ liệu master c<PERSON><PERSON> hệ thống.
            </div>
            
            <div class="row">
              <!-- Thành phố -->
              <%= link_to admin_cities_path, class: "col-md-6 col-lg-4 mb-4" do %>
                <div class="card management-card border-primary">
                  <div class="card-body text-center">
                    <i class="fas fa-city fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Thành phố</h5>
                    <p class="card-text text-muted">Quản lý danh sách các thành phố</p>
                    <button class="btn btn-primary">Quản lý</button>
                  </div>
                </div>
              <% end %>

              <!-- Đại học/Cao đẳng -->
              <%= link_to admin_colleges_path, class: "col-md-6 col-lg-4 mb-4" do %>
                <div class="card management-card border-success">
                  <div class="card-body text-center">
                    <i class="fas fa-university fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Đại học/Cao đẳng</h5>
                    <p class="card-text text-muted">Quản lý danh sách các trường đại học, cao đẳng</p>
                    <button class="btn btn-success">Quản lý</button>
                  </div>
                </div>
              <% end %>

              <!-- Giấy tờ tùy thân -->
              <%= link_to admin_identity_document_types_path, class: "col-md-6 col-lg-4 mb-4" do %>
                <div class="card management-card border-warning">
                  <div class="card-body text-center">
                    <i class="fas fa-id-card fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">Giấy tờ tùy thân</h5>
                    <p class="card-text text-muted">Quản lý các loại giấy tờ tùy thân</p>
                    <button class="btn btn-warning">Quản lý</button>
                  </div>
                </div>
              <% end %>

              <!-- Hỗ trợ đặc biệt -->
              <%= link_to admin_support_choices_path, class: "col-md-6 col-lg-4 mb-4" do %>
                <div class="card management-card border-info">
                  <div class="card-body text-center">
                    <i class="fas fa-hands-helping fa-3x text-info mb-3"></i>
                    <h5 class="card-title">Hỗ trợ đặc biệt</h5>
                    <p class="card-text text-muted">Quản lý các loại hỗ trợ đặc biệt</p>
                    <button class="btn btn-info">Quản lý</button>
                  </div>
                </div>
              <% end %>

              <!-- Lý do đăng ký thi -->
              <%= link_to admin_registration_reasons_path, class: "col-md-6 col-lg-4 mb-4" do %>
                <div class="card management-card border-secondary">
                  <div class="card-body text-center">
                    <i class="fas fa-question-circle fa-3x text-secondary mb-3"></i>
                    <h5 class="card-title">Lý do đăng ký thi</h5>
                    <p class="card-text text-muted">Quản lý các lý do đăng ký thi</p>
                    <button class="btn btn-secondary">Quản lý</button>
                  </div>
                </div>
              <% end %>

              <!-- Phương thức nhận chứng chỉ -->
              <%= link_to admin_certificate_delivery_methods_path, class: "col-md-6 col-lg-4 mb-4" do %>
                <div class="card management-card border-dark">
                  <div class="card-body text-center">
                    <i class="fas fa-certificate fa-3x text-dark mb-3"></i>
                    <h5 class="card-title">Phương thức nhận chứng chỉ</h5>
                    <p class="card-text text-muted">Quản lý phương thức nhận chứng chỉ</p>
                    <button class="btn btn-dark">Quản lý</button>
                  </div>
                </div>
              <% end %>
            </div>

            <hr class="my-4">
            
            <div class="row">
              <!-- Trung tâm thi -->
              <%= link_to admin_exam_centers_path, class: "col-md-6 col-lg-4 mb-4" do %>
                <div class="card management-card border-danger">
                  <div class="card-body text-center">
                    <i class="fas fa-building fa-3x text-danger mb-3"></i>
                    <h5 class="card-title">Trung tâm thi</h5>
                    <p class="card-text text-muted">Quản lý địa chỉ, hiển thị và sắp xếp trung tâm thi</p>
                    <button class="btn btn-danger">Quản lý</button>
                  </div>
                </div>
              <% end %>

              <!-- Ngày thi -->
              <%= link_to admin_exam_days_path, class: "col-md-6 col-lg-4 mb-4" do %>
                <div class="card management-card border-success">
                  <div class="card-body text-center">
                    <i class="fas fa-calendar-alt fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Ngày thi</h5>
                    <p class="card-text text-muted">Quản lý ngày thi cho từng trung tâm</p>
                    <button class="btn btn-success">Quản lý</button>
                  </div>
                </div>
              <% end %>

              <!-- Ca thi -->
              <%= link_to admin_exam_sessions_path, class: "col-md-6 col-lg-4 mb-4" do %>
                <div class="card management-card border-warning">
                  <div class="card-body text-center">
                    <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">Ca thi</h5>
                    <p class="card-text text-muted">Quản lý ca thi cho từng ngày</p>
                    <button class="btn btn-warning">Quản lý</button>
                  </div>
                </div>
              <% end %>
            </div>
            
            <hr class="my-4">
            
            <div class="row">
              <!-- Unified Management -->
              <%= link_to admin_exam_management_path, class: "col-md-12 mb-4" do %>
                <div class="card management-card border-purple unified-card">
                  <div class="card-body text-center p-4">
                    <div class="row align-items-center">
                      <div class="col-md-3">
                        <i class="fas fa-sitemap fa-4x text-purple mb-3"></i>
                      </div>
                      <div class="col-md-6">
                        <h4 class="card-title text-purple mb-2">
                          <i class="fas fa-star me-2"></i>Quản lý Thi cử Tổng hợp
                        </h4>
                        <p class="card-text text-muted">
                          Quản lý toàn bộ trung tâm thi, ngày thi và ca thi
                        </p>
                      </div>
                      <div class="col-md-3">
                        <span class="badge bg-purple text-white fs-6 mb-2">✨ Tính năng mới</span>
                        <br>
                        <button class="btn btn-purple btn-lg">Trải nghiệm ngay</button>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .container-wrapper {
    background-color: #F8F8F8;
    padding: 20px 0;
    min-height: 100vh;
  }

  .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }

  .card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
  }

  .management-card {
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
  }

  .management-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }

  .management-card .card-body {
    padding: 2rem;
  }

  .management-card .btn {
    min-width: 120px;
  }

  .border-primary { border-color: #007bff !important; }
  .border-success { border-color: #28a745 !important; }
  .border-warning { border-color: #ffc107 !important; }
  .border-info { border-color: #17a2b8 !important; }
  .border-secondary { border-color: #6c757d !important; }
  .border-danger { border-color: #dc3545 !important; }
  .border-dark { border-color: #343a40 !important; }
  .border-purple { border-color: #6f42c1 !important; }
  
  .text-purple { color: #6f42c1 !important; }
  .bg-purple { background-color: #6f42c1 !important; }
  .btn-purple { 
    background-color: #6f42c1; 
    border-color: #6f42c1; 
    color: white;
  }
  .btn-purple:hover { 
    background-color: #5a2d91; 
    border-color: #5a2d91; 
    color: white;
  }
  
  .unified-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none !important;
  }
  
  .unified-card .card-title,
  .unified-card .card-text {
    color: white !important;
  }
  
  .unified-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 1rem 2rem rgba(111, 66, 193, 0.3);
  }
</style> 