<div class="container-wrapper">
  <div class="container-lg">
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title mb-0">
              <i class="fas fa-clock me-2"></i>Quản lý Ca thi
            </h4>
          </div>
          <div class="card-body">
            <div class="alert alert-info mb-4">
              <i class="fas fa-info-circle me-2"></i>
              Chọn trung tâm thi và ngày thi, sau đó nhập các ca thi theo định dạng HH:MM - HH:MM.
            </div>
            
            <%= form_with url: admin_exam_sessions_path, method: :patch, local: true do |form| %>
              <div class="row mb-3">
                <div class="col-md-6">
                  <%= form.label :exam_center_id, "Chọn trung tâm thi:", class: "form-label" %>
                  <%= form.select :exam_center_id, options_for_select([['Chọn trung tâm thi', '']] + @exam_centers.map { |center| [center.address, center.id] }, params[:exam_center_id]), 
                      {}, { class: "form-control", id: "session-exam-center-select" } %>
                </div>
                <div class="col-md-6">
                  <%= form.label :exam_day_id, "Chọn ngày thi:", class: "form-label" %>
                  <%= form.select :exam_day_id, [['Chọn ngày thi', '']], 
                      {}, { class: "form-control", id: "session-exam-day-select", disabled: true } %>
                </div>
              </div>
              
              <div class="form-group mb-3">
                <label class="form-label">Danh sách ca thi:</label>
                <div id="exam-sessions-container" class="border rounded p-3" style="min-height: 200px; background-color: #f8f9fa;">
                  <div class="text-center text-muted py-4" id="empty-state-select">
                    <i class="fas fa-clock fa-3x mb-2"></i>
                    <p>Chọn trung tâm thi và ngày thi để xem danh sách ca thi</p>
                  </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-3">
                  <button type="button" class="btn btn-outline-success" id="add-exam-session-btn" disabled>
                    <i class="fas fa-plus me-2"></i>Thêm ca thi
                  </button>
                  <div class="form-text">
                    <i class="fas fa-info-circle me-1"></i>
                    Nhập ca thi theo định dạng HH:MM - HH:MM (ví dụ: 08:00 - 10:00)
                  </div>
                </div>
              </div>
              
              <div class="text-end mt-3">
                <%= link_to "Quay lại", admin_master_data_path, class: "btn btn-secondary me-2" %>
                <%= form.submit 'Cập nhật ca thi', class: 'btn btn-primary', disabled: true, id: 'update-session-button' %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const sessionExamCenterSelect = document.getElementById('session-exam-center-select');
  const sessionExamDaySelect = document.getElementById('session-exam-day-select');
  const examSessionsContainer = document.getElementById('exam-sessions-container');
  const emptyStateSelect = document.getElementById('empty-state-select');
  const addExamSessionBtn = document.getElementById('add-exam-session-btn');
  const updateSessionButton = document.getElementById('update-session-button');
  
  let examSessions = [];
  let nextId = 1;
  let hasSelectedCenterAndDay = false;
  
  // Get params from URL
  const urlParams = new URLSearchParams(window.location.search);
  const preselectedCenterId = urlParams.get('exam_center_id');
  const preselectedDayId = urlParams.get('exam_day_id');
  
  // Helper function to make AJAX requests
  function makeRequest(url, options = {}) {
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    };
    
    return fetch(url, { ...defaultOptions, ...options })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      });
  }
  
  // Create option element
  function createOption(value, text, disabled = false, selected = false) {
    const option = document.createElement('option');
    option.value = value;
    option.textContent = text;
    option.disabled = disabled;
    option.selected = selected;
    return option;
  }

  // Validate time range format (HH:MM - HH:MM)
  function validateTimeRange(timeRange) {
    const timeRangePattern = /^([01]?[0-9]|2[0-3]):[0-5][0-9]\s*-\s*([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRangePattern.test(timeRange.trim());
  }

  // Create exam session item HTML
  function createExamSessionItem(timeRange, isNew = false) {
    const id = `exam-session-${nextId++}`;
    const displayValue = isNew ? '' : timeRange;
    
    return `
      <div class="exam-session-item mb-2 p-3 border rounded bg-white" data-id="${id}">
        <div class="row align-items-center">
          <div class="col-md-8">
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-clock"></i>
              </span>
              <input type="text" 
                     class="form-control exam-session-input" 
                     value="${displayValue}"
                     placeholder="HH:MM - HH:MM (ví dụ: 08:00 - 10:00)"
                     maxlength="13">
              <div class="input-group-text session-display ${displayValue ? '' : 'text-muted'}">
                ${displayValue || 'Nhập ca thi'}
              </div>
            </div>
            <div class="invalid-feedback" style="display: none;">
              Vui lòng nhập đúng định dạng HH:MM - HH:MM
            </div>
          </div>
          <div class="col-md-4 text-end">
            <button type="button" class="btn btn-outline-danger btn-sm remove-exam-session-btn">
              <i class="fas fa-trash me-1"></i>Xóa
            </button>
          </div>
        </div>
      </div>
    `;
  }
  
  // Create empty state HTML
  function createEmptyState() {
    if (!hasSelectedCenterAndDay) {
      return `
        <div class="text-center text-muted py-4">
          <i class="fas fa-clock fa-3x mb-2"></i>
          <p>Chọn trung tâm thi và ngày thi để xem danh sách ca thi</p>
        </div>
      `;
    } else {
      return `
        <div class="text-center text-muted py-4">
          <i class="fas fa-info-circle fa-3x mb-2"></i>
          <p>Không có ca thi nào</p>
          <small class="text-muted">Bấm "Thêm ca thi" để thêm ca thi mới</small>
        </div>
      `;
    }
  }

  // Render exam sessions
  function renderExamSessions() {
    if (examSessions.length === 0) {
      examSessionsContainer.innerHTML = createEmptyState();
    } else {
      examSessionsContainer.innerHTML = examSessions.map(session => createExamSessionItem(session)).join('');
    }
    
    // Update submit button and hidden inputs
    updateSubmitState();
  }
  
  // Update submit button state and create hidden inputs
  function updateSubmitState() {
    const hasData = examSessions.length > 0 && examSessions.some(session => session.trim() !== '');
    updateSessionButton.disabled = !hasData;
    
    // Remove existing hidden inputs
    document.querySelectorAll('input[name="exam_sessions"]').forEach(input => input.remove());
    
    // Create hidden inputs for form submission
    if (hasData) {
      const form = document.querySelector('form');
      const textarea = document.createElement('textarea');
      textarea.name = 'exam_sessions';
      textarea.style.display = 'none';
      textarea.value = examSessions.filter(session => session.trim() !== '').join('\n');
      form.appendChild(textarea);
    }
  }
  
  // Event: Exam center change
  sessionExamCenterSelect.addEventListener('change', function() {
    const selectedCenterId = this.value;
    
    // Reset dependent elements
    sessionExamDaySelect.disabled = true;
    sessionExamDaySelect.innerHTML = '';
    sessionExamDaySelect.appendChild(createOption('', 'Chọn ngày thi', true, true));
    examSessions = [];
    addExamSessionBtn.disabled = true;
    hasSelectedCenterAndDay = false;
    renderExamSessions();
    
    if (selectedCenterId) {
      const params = new URLSearchParams({ exam_center_id: selectedCenterId });
      const url = '<%= load_by_center_admin_exam_days_path %>?' + params.toString();
      
      makeRequest(url)
        .then(data => {
          // Clear existing options
          sessionExamDaySelect.innerHTML = '';
          
          // Add default option
          sessionExamDaySelect.appendChild(
            createOption('', 'Chọn ngày thi', true, true)
          );
          
          // Add exam days
          data.forEach(examDay => {
            sessionExamDaySelect.appendChild(
              createOption(examDay.id, examDay.date)
            );
          });
          
          sessionExamDaySelect.disabled = false;
        })
        .catch(() => {
          console.error('Có lỗi xảy ra khi tải danh sách ngày thi!');
        });
    }
  });
  
  // Event: Exam day change
  sessionExamDaySelect.addEventListener('change', function() {
    const selectedCenterId = sessionExamCenterSelect.value;
    const selectedDayId = this.value;
    
    if (selectedDayId && selectedCenterId) {
      const params = new URLSearchParams({ 
        exam_center_id: selectedCenterId, 
        exam_day_id: selectedDayId 
      });
      const url = '<%= load_by_day_admin_exam_sessions_path %>?' + params.toString();
      
      makeRequest(url)
        .then(data => {
          examSessions = data || [];
          addExamSessionBtn.disabled = false;
          hasSelectedCenterAndDay = true;
          renderExamSessions();
        })
        .catch(() => {
          console.error('Có lỗi xảy ra khi tải danh sách ca thi!');
          examSessions = [];
          addExamSessionBtn.disabled = false;
          hasSelectedCenterAndDay = true;
          renderExamSessions();
        });
    } else {
      examSessions = [];
      addExamSessionBtn.disabled = true;
      hasSelectedCenterAndDay = false;
      renderExamSessions();
    }
  });
  
  // Event: Add exam session
  addExamSessionBtn.addEventListener('click', function() {
    examSessions.push('');
    renderExamSessions();
    
    // Focus on the new input
    const newItems = examSessionsContainer.querySelectorAll('.exam-session-item');
    const lastInput = newItems[newItems.length - 1]?.querySelector('.exam-session-input');
    if (lastInput) lastInput.focus();
  });
  
  // Event delegation for dynamic elements
  examSessionsContainer.addEventListener('click', function(e) {
    if (e.target.closest('.remove-exam-session-btn')) {
      const item = e.target.closest('.exam-session-item');
      const index = Array.from(examSessionsContainer.children).indexOf(item);
      examSessions.splice(index, 1);
      renderExamSessions();
    }
  });
  
  // Event delegation for time input changes
  examSessionsContainer.addEventListener('input', function(e) {
    if (e.target.classList.contains('exam-session-input')) {
      const input = e.target;
      const item = input.closest('.exam-session-item');
      const index = Array.from(examSessionsContainer.children).indexOf(item);
      const sessionDisplay = item.querySelector('.session-display');
      const invalidFeedback = item.querySelector('.invalid-feedback');
      
      const timeRange = input.value.trim();
      examSessions[index] = timeRange;
      
      if (timeRange) {
        sessionDisplay.textContent = timeRange;
        sessionDisplay.classList.remove('text-muted');
        
        if (validateTimeRange(timeRange)) {
          input.classList.remove('is-invalid');
          invalidFeedback.style.display = 'none';
        } else {
          input.classList.add('is-invalid');
          invalidFeedback.style.display = 'block';
        }
      } else {
        sessionDisplay.textContent = 'Nhập ca thi';
        sessionDisplay.classList.add('text-muted');
        input.classList.remove('is-invalid');
        invalidFeedback.style.display = 'none';
      }
      
      updateSubmitState();
    }
  });
  
  // Auto-load when page loads with preselected values
  if (preselectedCenterId) {
    sessionExamCenterSelect.value = preselectedCenterId;
    sessionExamCenterSelect.dispatchEvent(new Event('change'));
    
    // Wait for exam days to load, then select the day
    if (preselectedDayId) {
      const checkAndSelectDay = () => {
        if (!sessionExamDaySelect.disabled) {
          sessionExamDaySelect.value = preselectedDayId;
          sessionExamDaySelect.dispatchEvent(new Event('change'));
        } else {
          setTimeout(checkAndSelectDay, 100);
        }
      };
      setTimeout(checkAndSelectDay, 100);
    }
  }
});
</script>

<style>
  .container-wrapper {
    background-color: #F8F8F8;
    padding: 20px 0;
    min-height: 100vh;
  }

  .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }

  .card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
  }

  .form-control {
    border-radius: 0.375rem;
  }

  .form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }

  .form-control:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
  }

  .exam-session-item {
    transition: all 0.3s ease;
    animation: fadeIn 0.3s ease-in;
  }

  .exam-session-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  .exam-session-input.is-invalid {
    border-color: #dc3545;
  }

  .remove-exam-session-btn:hover {
    transform: scale(1.05);
  }

  #exam-sessions-container {
    max-height: 400px;
    overflow-y: auto;
  }

  .session-display {
    min-width: 140px;
    font-weight: 500;
    background-color: #f8f9fa !important;
    border-left: 1px solid #dee2e6 !important;
  }

  .session-display.text-muted {
    color: #6c757d !important;
    font-style: italic;
  }

  .exam-session-input {
    font-family: 'Courier New', monospace;
    letter-spacing: 0.5px;
  }

  .exam-session-input::placeholder {
    font-family: inherit;
    letter-spacing: normal;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .invalid-feedback {
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 0.25rem;
  }

  .input-group .form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
  }
</style> 