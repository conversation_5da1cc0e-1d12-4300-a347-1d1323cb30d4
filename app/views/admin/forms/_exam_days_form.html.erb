<div id="exam-day-settings" class="<%= defined?(hide_title) && hide_title ? 'border-0 section-wrapper mb-0' : 'border rounded section-wrapper mb-5' %>">
  <% unless defined?(hide_title) && hide_title %>
    <h3>
      <PERSON><PERSON><PERSON>
    </h3>
  <% end %>
  <div class="<%= defined?(hide_title) && hide_title ? 'mt-3' : 'mt-4' %>">
    <%= form_with url: update_exam_days_admin_forms_path, method: :put, local: false, id: 'exam-day-form', data: { form_id: 'exam-day-settings', turbo: false } do |form| %>
      <div class="form-group">
        <select class="form-control" id="exam-center-select" name="exam_center_id">
          <option value="" disabled selected>Chọn trung tâm thi</option>
          <% @exam_centers.map do |center| %>
            <option value="<%= center.id %>"><%= center.address %></option>
          <% end %>
        </select>
      </div>
      <div class="form-group mt-3">
        <%= form.text_area :exam_days, class: 'form-control', id: 'exam-days-textarea', rows: 5, disabled: true %>
      </div>
      <div class="text-end mt-3">
        <%= form.submit 'Cập nhật', class: 'btn btn-primary', disabled: true, id: 'update-button' %>
      </div>
    <% end %>
  </div>
  <div class="alert-container mt-3" style="display: none;"></div>
</div>

<script type="module">
  $(document).ready(function () {
    $('#exam-center-select').select2({
      theme: "bootstrap"
    });
    $('#exam-center-select').change(function () {
      var selectedCenterId = $(this).val();
      if (selectedCenterId) {
        $.ajax({
          url: '/admin/forms/load_exam_days',
          method: 'GET',
          data: {exam_center_id: selectedCenterId},
          success: function (data) {
            console.log("data", data);
            let text = "";
            $.each(data, function (index, item) {
              text = text + item.date.toString() + "\n";
            });
            $('#exam-days-textarea').val(text).prop('disabled', false);
            $('#update-button').prop('disabled', false);
          }
        });
      }
    });
  });
</script>
