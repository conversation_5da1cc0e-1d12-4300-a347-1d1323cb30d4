<div id="exam-center-visibility-settings" class="<%= defined?(hide_title) && hide_title ? 'border-0 section-wrapper mb-0' : 'border rounded section-wrapper mb-5' %>">
  <% unless defined?(hide_title) && hide_title %>
    <h3>
      <i class="fas fa-eye me-2"></i>Hiển thị Trung tâm thi
    </h3>
  <% end %>
  <div class="<%= defined?(hide_title) && hide_title ? 'mt-3' : 'mt-4' %>">
    <p class="text-muted mb-3">
      <i class="fas fa-info-circle me-1"></i>
      Chọn những trung tâm thi nào sẽ hiển thị cho người dùng khi đăng ký thi.
    </p>
    
    <%= form_with url: update_exam_center_visibility_admin_forms_path, method: :put, local: false, id: 'exam-center-visibility-form', data: { form_id: 'exam-center-visibility-settings', turbo: false } do |form| %>
      <div class="row">
        <% @exam_centers.each do |exam_center| %>
          <div class="col-md-6 mb-3">
            <div class="card border-left-primary h-100">
              <div class="card-body">
                <div class="d-flex align-items-start">
                  <div class="form-check me-3">
                    <%= check_box_tag "exam_center_ids[]", exam_center.id, exam_center.is_visible, 
                        class: "form-check-input", 
                        id: "exam_center_#{exam_center.id}" %>
                  </div>
                  <div class="flex-grow-1">
                    <%= label_tag "exam_center_#{exam_center.id}", class: "form-check-label" do %>
                      <div class="fw-bold text-primary">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        <%= exam_center.address %>
                      </div>
                      <small class="text-muted">
                        <i class="fas fa-calendar-alt me-1"></i>
                        <%= exam_center.exam_days.count %> ngày thi
                        <i class="fas fa-clock ms-2 me-1"></i>
                        <%= exam_center.exam_days.joins(:exam_sessions).count %> ca thi
                      </small>
                    <% end %>
                  </div>
                  <div class="ms-2">
                    <% if exam_center.is_visible? %>
                      <span class="badge bg-success">
                        <i class="fas fa-eye me-1"></i>Hiển thị
                      </span>
                    <% else %>
                      <span class="badge bg-secondary">
                        <i class="fas fa-eye-slash me-1"></i>Ẩn
                      </span>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
      
      <div class="text-end mt-4">
        <button type="button" class="btn btn-outline-secondary me-2" id="toggle-all-centers">
          <i class="fas fa-toggle-on me-1"></i>Chọn tất cả
        </button>
        <%= form.submit 'Cập nhật hiển thị', class: 'btn btn-primary' %>
      </div>
    <% end %>
  </div>
  <div class="alert-container mt-3" style="display: none;"></div>
</div>

<script type="module">
  $(document).ready(function () {
    // Toggle all checkboxes
    $('#toggle-all-centers').on('click', function() {
      const checkboxes = $('input[name="exam_center_ids[]"]');
      const allChecked = checkboxes.filter(':checked').length === checkboxes.length;
      
      checkboxes.prop('checked', !allChecked);
      
      // Update button text
      if (allChecked) {
        $(this).html('<i class="fas fa-toggle-on me-1"></i>Chọn tất cả');
      } else {
        $(this).html('<i class="fas fa-toggle-off me-1"></i>Bỏ chọn tất cả');
      }
      
      // Update badges
      updateBadges();
    });
    
    // Update badges when individual checkboxes change
    $('input[name="exam_center_ids[]"]').on('change', function() {
      updateBadges();
    });
    
    function updateBadges() {
      $('input[name="exam_center_ids[]"]').each(function() {
        const isChecked = $(this).is(':checked');
        const cardBody = $(this).closest('.card-body');
        const badge = cardBody.find('.badge');
        
        if (isChecked) {
          badge.removeClass('bg-secondary').addClass('bg-success')
               .html('<i class="fas fa-eye me-1"></i>Hiển thị');
        } else {
          badge.removeClass('bg-success').addClass('bg-secondary')
               .html('<i class="fas fa-eye-slash me-1"></i>Ẩn');
        }
      });
    }
  });
</script>

<style>
  .border-left-primary {
    border-left: 4px solid #007bff !important;
  }
  
  .card {
    transition: all 0.2s ease-in-out;
  }
  
  .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
  
  .form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
  }
  
  .badge {
    font-size: 0.75em;
  }
</style> 