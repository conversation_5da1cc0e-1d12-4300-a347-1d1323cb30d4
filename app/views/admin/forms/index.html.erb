<div class="container-wrapper">
  <div class="container-lg">
    <div class="row">
      <div class="col-md-12">
        <div class="accordion" id="adminFormsAccordion">
          
          <!-- Thành Phố Section -->
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#cities-collapse" aria-expanded="true" aria-controls="cities-collapse">
                <i class="fas fa-city me-2"></i>Thành Phố
              </button>
            </h3>
            <div id="cities-collapse" class="accordion-collapse collapse show" data-bs-parent="#adminFormsAccordion">
              <div class="accordion-body p-0">
                <%= render partial: 'master_data_update_form', locals: {
                  element_id: "cities-settings",
                  title: "",
                  update_url_path: update_cities_admin_forms_path,
                  params_attr: :cities,
                  initial_value: @cities_name,
                  hide_title: true
                } %>
              </div>
            </div>
          </div>

          <!-- <PERSON><PERSON><PERSON> h<PERSON>/<PERSON> Đẳng Section -->
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#colleges-collapse" aria-expanded="false" aria-controls="colleges-collapse">
                <i class="fas fa-university me-2"></i>Đại học/Cao Đẳng
              </button>
            </h3>
            <div id="colleges-collapse" class="accordion-collapse collapse" data-bs-parent="#adminFormsAccordion">
              <div class="accordion-body p-0">
                <%= render partial: 'master_data_update_form', locals: {
                  element_id: "colleges-settings",
                  title: "",
                  update_url_path: update_colleges_admin_forms_path,
                  params_attr: :colleges,
                  initial_value: @colleges_name,
                  hide_title: true
                } %>
              </div>
            </div>
          </div>

          <!-- Giấy tờ tùy thân Section -->
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#identity-document-collapse" aria-expanded="false" aria-controls="identity-document-collapse">
                <i class="fas fa-id-card me-2"></i>Giấy tờ tùy thân
              </button>
            </h3>
            <div id="identity-document-collapse" class="accordion-collapse collapse" data-bs-parent="#adminFormsAccordion">
              <div class="accordion-body p-0">
                <%= render partial: 'master_data_update_form', locals: {
                  element_id: "identity-document-settings",
                  title: "",
                  update_url_path: update_identity_document_types_admin_forms_path,
                  params_attr: :identity_document_types,
                  initial_value: @identity_document_types,
                  hide_title: true
                } %>
              </div>
            </div>
          </div>

          <!-- Lý do đăng ký thi Section -->
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#registration-reason-collapse" aria-expanded="false" aria-controls="registration-reason-collapse">
                <i class="fas fa-question-circle me-2"></i>Lý do đăng ký thi
              </button>
            </h3>
            <div id="registration-reason-collapse" class="accordion-collapse collapse" data-bs-parent="#adminFormsAccordion">
              <div class="accordion-body p-0">
                <%= render partial: 'master_data_update_form', locals: {
                  element_id: "registration-reason-settings",
                  title: "",
                  update_url_path: update_registration_reasons_admin_forms_path,
                  params_attr: :registration_reasons,
                  initial_value: @registration_reasions,
                  hide_title: true
                } %>
              </div>
            </div>
          </div>

          <!-- Hỗ trợ đặc biệt Section -->
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#support-choice-collapse" aria-expanded="false" aria-controls="support-choice-collapse">
                <i class="fas fa-hands-helping me-2"></i>Hỗ trợ đặc biệt
              </button>
            </h3>
            <div id="support-choice-collapse" class="accordion-collapse collapse" data-bs-parent="#adminFormsAccordion">
              <div class="accordion-body p-0">
                <%= render partial: 'master_data_update_form', locals: {
                  element_id: "support-choice-settings",
                  title: "",
                  update_url_path: update_support_choices_admin_forms_path,
                  params_attr: :support_choices,
                  initial_value: @support_choices,
                  hide_title: true
                } %>
              </div>
            </div>
          </div>

          <!-- Phương thức nhận chứng chỉ Section -->
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#certificate-delivery-collapse" aria-expanded="false" aria-controls="certificate-delivery-collapse">
                <i class="fas fa-certificate me-2"></i>Phương thức nhận chứng chỉ
              </button>
            </h3>
            <div id="certificate-delivery-collapse" class="accordion-collapse collapse" data-bs-parent="#adminFormsAccordion">
              <div class="accordion-body p-0">
                <%= render partial: 'master_data_update_form', locals: {
                  element_id: "certificate-delivery-method-settings",
                  title: "",
                  update_url_path: update_certificate_delivery_methods_admin_forms_path,
                  params_attr: :certificate_delivery_methods,
                  initial_value: @certificate_delivery_methods,
                  hide_title: true
                } %>
              </div>
            </div>
          </div>

          <!-- Trung tâm thi Section -->
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#exam-center-collapse" aria-expanded="false" aria-controls="exam-center-collapse">
                <i class="fas fa-building me-2"></i>Trung tâm thi (Quản lý địa chỉ)
              </button>
            </h3>
            <div id="exam-center-collapse" class="accordion-collapse collapse" data-bs-parent="#adminFormsAccordion">
              <div class="accordion-body p-0">
                <%= render partial: 'master_data_update_form', locals: {
                  element_id: "exam-center-settings",
                  title: "",
                  update_url_path: update_exam_centers_admin_forms_path,
                  params_attr: :exam_center_addresses,
                  initial_value: @exam_center_addresses,
                  hide_title: true
                } %>
              </div>
            </div>
          </div>

          <!-- Hiển thị Trung tâm thi Section -->
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#exam-center-visibility-collapse" aria-expanded="false" aria-controls="exam-center-visibility-collapse">
                <i class="fas fa-eye me-2"></i>Hiển thị Trung tâm thi
              </button>
            </h3>
            <div id="exam-center-visibility-collapse" class="accordion-collapse collapse" data-bs-parent="#adminFormsAccordion">
              <div class="accordion-body p-0">
                <%= render partial: 'exam_center_visibility_form', locals: { hide_title: true } %>
              </div>
            </div>
          </div>

          <!-- Ngày Thi Section -->
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#exam-days-collapse" aria-expanded="false" aria-controls="exam-days-collapse">
                <i class="fas fa-calendar-alt me-2"></i>Ngày Thi
              </button>
            </h3>
            <div id="exam-days-collapse" class="accordion-collapse collapse" data-bs-parent="#adminFormsAccordion">
              <div class="accordion-body p-0">
                <%= render partial: 'exam_days_form', locals: { exam_centers: @exam_centers, hide_title: true } %>
              </div>
            </div>
          </div>

          <!-- Ca Thi Section -->
          <div class="accordion-item">
            <h3 class="accordion-header">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#exam-sessions-collapse" aria-expanded="false" aria-controls="exam-sessions-collapse">
                <i class="fas fa-clock me-2"></i>Ca Thi
              </button>
            </h3>
            <div id="exam-sessions-collapse" class="accordion-collapse collapse" data-bs-parent="#adminFormsAccordion">
              <div class="accordion-body p-0">
                <%= render partial: 'exam_session_form', locals: { exam_centers: @exam_centers, hide_title: true } %>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>

<script type="module">
  $(document).ready(function () {
    $('#settings-nav a').on('click', function (e) {
      e.preventDefault();
      let target = $($(this).attr('href'));
      console.log(target);
      if (target.length) {
        $('html, body').animate({
          scrollTop: target.offset().top - 100
        }, 0);
      }
    });

    $('#exam-center-select').select2({
      theme: "bootstrap"
    });
    $('#exam-center-select').change(function () {
      var selectedCenterId = $(this).val();
      if (selectedCenterId) {
        $.ajax({
          url: '/admin/forms/load_exam_days',
          method: 'GET',
          data: {exam_center_id: selectedCenterId},
          success: function (data) {
            console.log("data", data);
            let text = "";
            $.each(data, function (index, item) {
              text = text + item.date.toString() + "\n";
            });
            $('#exam-days-textarea').val(text).prop('disabled', false);
            $('#update-button').prop('disabled', false);
          }
        });
      }
    });
  });
</script>

<style>
  #settings-nav .nav-link {
    color: black;
    text-decoration: none;
  }

  #settings-nav .nav-link:hover {
    color: white;
    background-color: #007bff;
    border-radius: 0.25rem;
  }

  .container-wrapper {
    background-color: #F8F8F8;
    padding: 20px 0;
  }

  .section-wrapper {
    background-color: #FFF;
    padding: 25px 30px;
  }

  .side-bar {
    position: fixed;
    top: 100px;
  }

  .alert-container {
    position: relative;
    z-index: 1050;
  }

  .alert-container .alert {
    margin-bottom: 0;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }

  /* Accordion Styles */
  .accordion-button {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    padding: 1rem 1.25rem;
  }

  .accordion-button:not(.collapsed) {
    background-color: #e9ecef;
    color: #000;
    box-shadow: none;
  }

  .accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    border-color: #86b7fe;
  }

  .accordion-button::after {
    margin-left: auto;
  }

  .accordion-item {
    border: 1px solid #dee2e6;
    margin-bottom: 0.5rem;
    border-radius: 0.375rem !important;
    overflow: hidden;
  }

  .accordion-item:first-of-type {
    border-top: 1px solid #dee2e6;
  }

  .accordion-body {
    padding: 1.25rem;
    background-color: #fff;
  }

  .accordion-header {
    margin-bottom: 0;
  }

  .accordion-button i {
    color: #6c757d;
  }

  .accordion-button:not(.collapsed) i {
    color: #0d6efd;
  }
</style>
