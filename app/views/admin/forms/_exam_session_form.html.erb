<div id="exam-sessions-settings" class="<%= defined?(hide_title) && hide_title ? 'border-0 section-wrapper mb-0' : 'border rounded section-wrapper mb-5' %>">
  <% unless defined?(hide_title) && hide_title %>
    <h3>
      Ca Thi
    </h3>
  <% end %>
  <div class="<%= defined?(hide_title) && hide_title ? 'mt-3' : 'mt-4' %>">
    <%= form_with url: update_exam_sessions_admin_forms_path, method: :put, local: false, data: { form_id: 'exam-sessions-settings', turbo: false } do |form| %>
      <div class="d-flex flex-column gap-3">
        <select class="form-control" id="session-exam-center-select" name="exam_center_id">
          <option value="" disabled selected>Chọn trung tâm thi</option>
          <% @exam_centers.map do |center| %>
            <option value="<%= center.id %>"><%= center.address %></option>
          <% end %>
        </select>
        <select class="form-control" id="session-exam-day-select" name="exam_day_id" disabled>
          <option value="" disabled selected>Chọn ngày thi</option>
        </select>
      </div>
      <div class="form-group mt-3">
        <%= form.text_area :exam_sessions, class: 'form-control', id: 'exam-sessions-textarea', rows: 5, disabled: true %>
      </div>
      <div class="text-end mt-3">
        <%= form.submit 'Cập nhật', class: 'btn btn-primary', disabled: true, id: 'update-session-button' %>
      </div>
    <% end %>
  </div>
  <div class="alert-container mt-3" style="display: none;"></div>
</div>

<script type="module">
  $(document).ready(function () {
    $('#session-exam-center-select').select2({
      theme: "bootstrap",
    });
    $('#session-exam-center-select').change(function () {
      let selectedCenterId = $(this).val();
      $('#session-exam-day-select').prop('disabled', true);
      $('#exam-sessions-textarea').val('').prop("disabled", true);
      $('#update-session-button').prop('disabled', true);
      if (selectedCenterId) {
        $.ajax({
          url: '/admin/forms/load_exam_days',
          method: 'GET',
          data: {exam_center_id: selectedCenterId},
          success: function (data) {
            $('#session-exam-day-select').empty();
            $('#session-exam-day-select').append(
              $('<option></option>', {
                value: "",
                text: "Chọn ngày thi"
              }).prop({
                disabled: true,
                selected: true
              })
            );

            $.each(data, function (index, exam_day) {
              $('#session-exam-day-select').append(
                $('<option></option>', {
                  value: exam_day.id,
                  text: exam_day.date
                })
              );
            });
            $('#session-exam-day-select').prop('disabled', false);
          }
        });
      }
    });
    $('#session-exam-day-select').select2({
      theme: "bootstrap"
    });
    $('#session-exam-day-select').change(function () {
      let selectedCenterId = $('#session-exam-center-select').val();
      let selectedDayId = $(this).val();
      if (selectedDayId && selectedCenterId) {
        $.ajax({
          url: '/admin/forms/load_exam_sessions',
          method: 'GET',
          data: {exam_center_id: selectedCenterId, exam_day_id: selectedDayId},
          success: function (data) {
            $('#exam-sessions-textarea').val(data.join("\n")).prop("disabled", false);
            $('#update-session-button').prop('disabled', false);
          }
        });
      }
    });
  });
</script>
