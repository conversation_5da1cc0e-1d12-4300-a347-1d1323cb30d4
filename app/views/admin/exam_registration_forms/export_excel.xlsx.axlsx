wb = xlsx_package.workbook

wb.add_worksheet(name: "Đơn đăng ký thi") do |sheet|
    # Create the header row
    sheet.add_row [
        "#", "<PERSON><PERSON><PERSON>", "Trung tâm thi", "<PERSON><PERSON><PERSON>hi", "<PERSON><PERSON> Thi", "<PERSON><PERSON><PERSON> độ thi", "<PERSON><PERSON><PERSON> tác",
        "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> sinh", "<PERSON><PERSON><PERSON><PERSON> tính",
        "Điện thoại", "<PERSON><PERSON>", "Địa chỉ liên hệ", "Gi<PERSON>y tờ tùy thân", "Số giấy",
        "<PERSON><PERSON><PERSON> hạn", "<PERSON><PERSON><PERSON> cấp", "<PERSON><PERSON> do đăng ký", "<PERSON><PERSON>ơng thức nhận chứng chỉ",
        "Cần hỗ trợ đặc biệt", "Loại hỗ trợ", "Mặt trước thẻ", "Mặt sau thẻ",
        "Ảnh chân dung"
    ]
    @exam_registration_data.each do |e|
        delivery_method = Master::CertificateDeliveryMethod.find_by(name: e.certificate_delivery_method)
        certificate_delivery_method_text = delivery_method&.description ||
                                           e.certificate_delivery_method ||
                                           'Không xác định'

        # Need specific support
        need_support_text = e.need_specific_support ? 'Có' : 'Không'

        # Support choice
        support_choice_text = ''
        if e.support_choice.present?
            support_choice_obj = Master::SupportChoice.find_by(name: e.support_choice)
            support_choice_text = support_choice_obj&.description || e.support_choice
        end

        # Work location with details
        work_location_text = e.work_location_type
        if e.college_name.present?
            work_location_text += " - #{e.college_name}"
        elsif e.company_name.present?
            work_location_text += " - #{e.company_name}"
        end

        # Combined registration reason
        registration_reason_text = e.registration_reason
        if e.other_registration_reason.present?
            registration_reason_text += " | Chi tiết: #{e.other_registration_reason}"
        end

        # Combined certificate delivery method
        certificate_delivery_combined = certificate_delivery_method_text
        certificate_delivery_combined += " | Địa chỉ: #{e.delivery_address}" if e.delivery_address.present?

        # Combined support choice
        support_choice_combined = support_choice_text
        support_choice_combined += " | Chi tiết: #{e.other_difficulty}" if e.other_difficulty.present?

        sheet.add_row [
            e.id,
            format_datetime(e.created_at),
            e.exam_center_name,
            e.exam_day,
            e.exam_time_range,
            e.exam_level.presence || 'Chưa chọn',
            work_location_text,
            e.lastname,
            e.firstname,
            format_date(e.birthday),
            e.gender,
            e.phone_number,
            e.email,
            e.detailed_contact_address,
            e.id_document_type,
            e.id_document_number,
            format_date(e.id_expiry_date),
            e.id_issue_place,
            registration_reason_text,
            certificate_delivery_combined,
            need_support_text,
            support_choice_combined,
            rails_blob_url(e.id_front_image, only_path: false),
            e.id_back_image.present? ? rails_blob_url(e.id_back_image, only_path: false) : nil,
            rails_blob_url(e.profile_image, only_path: false)
        ]
    end
end
