<div class="container-fluid p-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="text-primary">Chi tiết đơn đăng ký thi #<%= @exam_registration_form.id %></h2>
    <%= link_to "← Quay lại danh sách", admin_exam_registration_forms_path, class: "btn btn-secondary" %>
  </div>

  <div class="row">
    <!-- Thông tin cơ bản -->
    <div class="col-md-6">
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-user"></i> Thông tin cá nhân</h5>
        </div>
        <div class="card-body">
          <table class="table table-borderless">
            <tr>
              <th width="40%">Họ và tên:</th>
              <td><%= @exam_registration_form.lastname %> <%= @exam_registration_form.firstname %></td>
            </tr>
            <tr>
              <th>Ngày sinh:</th>
              <td><%= format_date(@exam_registration_form.birthday) %></td>
            </tr>
            <tr>
              <th>Giới tính:</th>
              <td><%= @exam_registration_form.gender %></td>
            </tr>
            <tr>
              <th>Số điện thoại:</th>
              <td><%= @exam_registration_form.phone_number %></td>
            </tr>
            <tr>
              <th>Email:</th>
              <td><%= @exam_registration_form.email %></td>
            </tr>
            <tr>
              <th>Địa chỉ liên hệ:</th>
              <td><%= @exam_registration_form.detailed_contact_address %></td>
            </tr>
          </table>
        </div>
      </div>

      <!-- Thông tin thi -->
      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> Thông tin thi</h5>
        </div>
        <div class="card-body">
          <table class="table table-borderless">
            <tr>
              <th width="40%">Trung tâm thi:</th>
              <td><%= @exam_registration_form.exam_center_name %></td>
            </tr>
            <tr>
              <th>Ngày thi:</th>
              <td><%= @exam_registration_form.exam_day %></td>
            </tr>
            <tr>
              <th>Ca thi:</th>
              <td><%= @exam_registration_form.exam_time_range %></td>
            </tr>
            <tr>
              <th>Cấp độ thi:</th>
              <td>
                <% if @exam_registration_form.exam_level.present? %>
                  <span class="badge bg-primary fs-6"><%= @exam_registration_form.exam_level %></span>
                <% else %>
                  <span class="text-muted">Chưa chọn</span>
                <% end %>
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>

    <!-- Thông tin làm việc -->
    <div class="col-md-6">
      <div class="card mb-4">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0"><i class="fas fa-briefcase"></i> Thông tin công tác</h5>
        </div>
        <div class="card-body">
          <table class="table table-borderless">
            <tr>
              <th width="40%">Loại đơn vị:</th>
              <td><%= @exam_registration_form.work_location_type %></td>
            </tr>
            <% if @exam_registration_form.college_name.present? %>
              <tr>
                <th>Trường/Học viện:</th>
                <td><%= @exam_registration_form.college_name %></td>
              </tr>
            <% elsif @exam_registration_form.company_name.present? %>
              <tr>
                <th>Công ty:</th>
                <td><%= @exam_registration_form.company_name %></td>
              </tr>
            <% end %>
          </table>
        </div>
      </div>

      <!-- Thông tin thanh toán -->
      <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0"><i class="fas fa-credit-card"></i> Thông tin thanh toán</h5>
        </div>
        <div class="card-body">
          <table class="table table-borderless">
            <tr>
              <th width="40%">Trạng thái:</th>
              <td>
                <% case @exam_registration_form.payment_status %>
                <% when 'pending' %>
                  <span class="badge bg-warning">Chờ thanh toán</span>
                <% when 'paid' %>
                  <span class="badge bg-success">Đã thanh toán</span>
                <% when 'failed' %>
                  <span class="badge bg-danger">Thanh toán thất bại</span>
                <% end %>
              </td>
            </tr>
            <tr>
              <th>Số tiền:</th>
              <td><%= number_to_currency(@exam_registration_form.payment_amount, unit: "VNĐ", separator: ",", delimiter: ".") %></td>
            </tr>
            <% if @exam_registration_form.payment_confirmed_at.present? %>
              <tr>
                <th>Xác nhận lúc:</th>
                <td><%= format_datetime(@exam_registration_form.payment_confirmed_at) %></td>
              </tr>
            <% end %>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Thông tin giấy tờ tùy thân -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
          <h5 class="mb-0"><i class="fas fa-id-card"></i> Thông tin giấy tờ tùy thân</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <table class="table table-borderless">
                <tr>
                  <th width="40%">Loại giấy tờ:</th>
                  <td><%= @exam_registration_form.id_document_type %></td>
                </tr>
                <tr>
                  <th>Số giấy tờ:</th>
                  <td><%= @exam_registration_form.id_document_number %></td>
                </tr>
                <tr>
                  <th>Ngày hết hạn:</th>
                  <td><%= format_date(@exam_registration_form.id_expiry_date) %></td>
                </tr>
                <tr>
                  <th>Nơi cấp:</th>
                  <td><%= @exam_registration_form.id_issue_place %></td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <h6 class="text-muted mb-3">Hình ảnh giấy tờ:</h6>
              <div class="row">
                <div class="col-4">
                  <% if @exam_registration_form.id_front_image.attached? %>
                    <%= link_to url_for(@exam_registration_form.id_front_image), target: "_blank", class: "text-decoration-none" do %>
                      <div class="border p-2 text-center">
                        <i class="fas fa-image fa-2x text-primary"></i>
                        <div class="small mt-1">Mặt trước</div>
                      </div>
                    <% end %>
                  <% else %>
                    <div class="border p-2 text-center text-muted">
                      <i class="fas fa-image fa-2x"></i>
                      <div class="small mt-1">Chưa có</div>
                    </div>
                  <% end %>
                </div>
                <div class="col-4">
                  <% if @exam_registration_form.id_back_image.attached? %>
                    <%= link_to url_for(@exam_registration_form.id_back_image), target: "_blank", class: "text-decoration-none" do %>
                      <div class="border p-2 text-center">
                        <i class="fas fa-image fa-2x text-primary"></i>
                        <div class="small mt-1">Mặt sau</div>
                      </div>
                    <% end %>
                  <% else %>
                    <div class="border p-2 text-center text-muted">
                      <i class="fas fa-image fa-2x"></i>
                      <div class="small mt-1">Chưa có</div>
                    </div>
                  <% end %>
                </div>
                <div class="col-4">
                  <% if @exam_registration_form.profile_image.attached? %>
                    <%= link_to url_for(@exam_registration_form.profile_image), target: "_blank", class: "text-decoration-none" do %>
                      <div class="border p-2 text-center">
                        <i class="fas fa-user fa-2x text-primary"></i>
                        <div class="small mt-1">Chân dung</div>
                      </div>
                    <% end %>
                  <% else %>
                    <div class="border p-2 text-center text-muted">
                      <i class="fas fa-user fa-2x"></i>
                      <div class="small mt-1">Chưa có</div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Thông tin bổ sung -->
  <div class="row">
    <div class="col-md-6">
      <div class="card mb-4">
        <div class="card-header bg-dark text-white">
          <h5 class="mb-0"><i class="fas fa-question-circle"></i> Lý do đăng ký</h5>
        </div>
        <div class="card-body">
          <p><strong><%= @exam_registration_form.registration_reason %></strong></p>
          <% if @exam_registration_form.other_registration_reason.present? %>
            <hr>
            <h6 class="text-muted">Chi tiết:</h6>
            <p class="text-muted"><%= @exam_registration_form.other_registration_reason %></p>
          <% end %>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-certificate"></i> Phương thức nhận chứng chỉ</h5>
        </div>
        <div class="card-body">
          <% delivery_method = Master::CertificateDeliveryMethod.find_by(name: @exam_registration_form.certificate_delivery_method) %>
          <p><strong>
            <% if delivery_method %>
              <%= delivery_method.description %>
            <% else %>
              Không xác định
            <% end %>
          </strong></p>
          <% if @exam_registration_form.delivery_address.present? %>
            <hr>
            <h6 class="text-muted">Địa chỉ giao hàng:</h6>
            <p class="text-muted"><%= @exam_registration_form.delivery_address %></p>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Hỗ trợ đặc biệt -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0"><i class="fas fa-hands-helping"></i> Hỗ trợ đặc biệt</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>Cần hỗ trợ:</h6>
              <% if @exam_registration_form.need_specific_support %>
                <span class="badge bg-success fs-6">Có</span>
              <% else %>
                <span class="badge bg-secondary fs-6">Không</span>
              <% end %>
            </div>
            <% if @exam_registration_form.need_specific_support %>
              <div class="col-md-6">
                <h6>Loại hỗ trợ:</h6>
                <% if @exam_registration_form.support_choice.present? %>
                  <% support_choice = Master::SupportChoice.find_by(name: @exam_registration_form.support_choice) %>
                  <p><strong>
                    <% if support_choice %>
                      <%= support_choice.name %>
                    <% else %>
                      <%= @exam_registration_form.support_choice %>
                    <% end %>
                  </strong></p>
                  <% if @exam_registration_form.other_difficulty.present? %>
                    <hr>
                    <h6 class="text-muted">Chi tiết khó khăn:</h6>
                    <p class="text-muted"><%= @exam_registration_form.other_difficulty %></p>
                  <% end %>
                <% else %>
                  <p class="text-muted">Chưa chọn</p>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Thông tin hệ thống -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header bg-light">
          <h5 class="mb-0 text-muted"><i class="fas fa-info-circle"></i> Thông tin hệ thống</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <table class="table table-borderless table-sm">
                <tr>
                  <th width="40%">Ngày đăng ký:</th>
                  <td><%= format_datetime(@exam_registration_form.created_at) %></td>
                </tr>
                <tr>
                  <th>Cập nhật cuối:</th>
                  <td><%= format_datetime(@exam_registration_form.updated_at) %></td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <% if @exam_registration_form.sepay_payment_code.present? %>
                <table class="table table-borderless table-sm">
                  <tr>
                    <th width="40%">Mã thanh toán:</th>
                    <td><%= @exam_registration_form.sepay_payment_code %></td>
                  </tr>
                </table>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .card-header h5 {
    margin-bottom: 0;
  }
  
  .table th {
    border: none;
    color: #6c757d;
    font-weight: 600;
  }
  
  .table td {
    border: none;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
  
  .badge.fs-6 {
    font-size: 0.9rem !important;
    padding: 0.5rem 0.75rem;
  }
</style> 