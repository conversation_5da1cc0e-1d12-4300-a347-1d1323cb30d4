<div class="container-fluid p-3">
  <div class="d-flex my-1 align-items-center px-2">
    <span class="me-3 fw-bold">Lọc:</span>
    <%= search_form_for @q, url: admin_exam_registration_forms_path, method: :get do |f| %>
      <div class="d-flex gap-3">
        <div>
          <%= f.select :exam_center_name_cont, options_for_select(@exam_center_addresses, @selected_exam_center_name), { include_blank: 'Tất cả các trung tâm thi của hệ thống' }, class: "form-select center-filter", id: "center-filter" %>
        </div>
        <div>
          <%= f.select :payment_status_eq, options_for_select(@payment_statuses, @selected_payment_status), { include_blank: 'Tất cả trạng thái thanh toán' }, class: "form-select payment-status-filter", id: "payment-status-filter" %>
        </div>
      </div>
      <%= f.submit "Lọc", class: "btn btn-secondary", style: "display: none" %>
    <% end %>
    <div class="ms-auto my-3">
      <%= link_to "Xuất file Excel", export_excel_admin_exam_registration_forms_path(format: :xlsx, q: params[:q]&.permit!), class: 'btn btn-primary' %>
    </div>
  </div>
  <div class="table-responsive mt-3" style="overflow-x: auto;">
    <table class="table table-hover" style="width: auto;">
      <thead class="table-light">
      <tr>
        <th>
          <div style="width: 60px">#</div>
        </th>
        <th>
          <div style="width: 100px">Ngày</div>
        </th>
        <th>
          <div style="width: 300px"> Trung tâm thi</div>
        </th>
        <th>
          <div style="width: 100px"> Ngày thi</div>
        </th>
        <th>
          <div style="width: 160px">Ca Thi</div>
        </th>
        <th>
          <div style="width: 80px">Cấp độ thi</div>
        </th>
        <th>
          <div style="width: 250px">Công tác</div>
        </th>
        <th>
          <div style="width: 160px">Họ</div>
        </th>
        <th>
          <div style="width: 160px">Tên</div>
        </th>
        <th>
          <div style="width: 100px">Ngày sinh</div>
        </th>
        <th>
          <div style="width: 60px">Giới tính</div>
        </th>
        <th>
          <div style="width: 90px">Điện thoại</div>
        </th>
        <th>
          <div style="width: 160px">Email</div>
        </th>
        <th>
          <div style="width: 200px">Địa chỉ liên hệ</div>
        </th>
        <th>
          <div style="width: 140px">Giấy tờ tùy thân</div>
        </th>
        <th>
          <div style="width: 180px">Số giấy</div>
        </th>
        <th>
          <div style="width: 80px">Hết hạn</div>
        </th>
        <th>
          <div style="width: 200px">Nơi cấp</div>
        </th>
        <th>
          <div style="width: 300px">Lý do đăng ký</div>
        </th>
        <th>
          <div style="width: 250px">Phương thức nhận chứng chỉ</div>
        </th>
        <th>
          <div style="width: 120px">Cần hỗ trợ đặc biệt</div>
        </th>
        <th>
          <div style="width: 250px">Loại hỗ trợ</div>
        </th>
        <th>
          <div style="width: 100px">Mặt trước thẻ</div>
        </th>
        <th>
          <div style="width: 100px">Mặt sau thẻ</div>
        </th>
        <th>
          <div style="width: 100px">Ảnh chân dung</div>
        </th>
        <th>
          <div style="width: 120px">Trạng thái thanh toán</div>
        </th>
        <th>
          <div style="width: 300px">Ghi chú thanh toán</div>
        </th>
        <th>
          <div style="width: 100px">Thao tác</div>
        </th>
      </tr>
      </thead>
      <tbody>
      <% @exam_registration_forms.each do |e| %>
        <tr>
          <td><%= link_to e.id, admin_exam_registration_form_path(e), class: "text-primary fw-bold text-decoration-none" %></td>
          <td><%= format_datetime e.created_at %></td>
          <td><%= e.exam_center_name %></td>
          <td><%= e.exam_day %></td>
          <td><%= e.exam_time_range %></td>
          <td>
            <% if e.exam_level.present? %>
              <span class="badge bg-primary"><%= e.exam_level %></span>
            <% else %>
              <span class="text-muted">-</span>
            <% end %>
          </td>
          <td>
            <%= e.work_location_type %>
            <% if e.college_name.present? %>
              <br><small class="text-muted"><%= e.college_name %></small>
            <% elsif e.company_name.present? %>
              <br><small class="text-muted"><%= e.company_name %></small>
            <% end %>
          </td>
          <td>
            <%= e.lastname %>
          </td>
          <td><%= e.firstname %></td>
          <td><%= format_date(e.birthday) %></td>
          <td><%= e.gender %></td>
          <td><%= e.phone_number %></td>
          <td><%= e.email %></td>
          <td><%= e.detailed_contact_address %></td>
          <td><%= e.id_document_type %></td>
          <td><%= e.id_document_number %></td>
          <td><%= format_date e.id_expiry_date %></td>
          <td><%= e.id_issue_place %></td>
          <td>
            <%= e.registration_reason %>
            <% if e.other_registration_reason.present? %>
              <br><small class="text-muted"><strong>Chi tiết:</strong> <%= truncate(e.other_registration_reason, length: 80) %></small>
            <% end %>
          </td>
          <td>
            <% delivery_method = Master::CertificateDeliveryMethod.find_by(name: e.certificate_delivery_method) %>
            <% if delivery_method %>
              <%= delivery_method.description %>
            <% else %>
              Không xác định
            <% end %>
            <% if e.delivery_address.present? %>
              <br><small class="text-muted"><strong>Địa chỉ:</strong> <%= truncate(e.delivery_address, length: 80) %></small>
            <% end %>
          </td>
          <td>
            <% if e.need_specific_support %>
              <span class="badge bg-success">Có</span>
            <% else %>
              <span class="badge bg-secondary">Không</span>
            <% end %>
          </td>
          <td>
            <% if e.support_choice.present? %>
              <% support_choice = Master::SupportChoice.find_by(name: e.support_choice) %>
              <% if support_choice %>
                <%= support_choice.name %>
              <% else %>
                <%= e.support_choice %>
              <% end %>
              <% if e.other_difficulty.present? %>
                <br><small class="text-muted"><strong>Chi tiết:</strong> <%= truncate(e.other_difficulty, length: 80) %></small>
              <% end %>
            <% else %>
              <span class="text-muted">-</span>
            <% end %>
          </td>
          <td><%= link_to "Mặt trước", url_for(e.id_front_image), target: "_blank" if e.id_front_image.attached? %></td>
          <td><%= link_to "Mặt sau", url_for(e.id_back_image), target: "_blank" if e.id_back_image.attached? %></td>
          <td><%= link_to "Chân dung", url_for(e.profile_image), target: "_blank" if e.profile_image.attached? %></td>
          <td>
            <% case e.payment_status %>
            <% when 'pending' %>
              <span class="badge bg-warning">Chờ thanh toán</span>
            <% when 'paid' %>
              <span class="badge bg-success">Đã thanh toán</span>
            <% when 'failed' %>
              <span class="badge bg-danger">Thanh toán thất bại</span>
            <% end %>
          </td>
          <td>
            <% if e.payment_note.present? %>
              <small class="text-muted"><%= truncate(e.payment_note, length: 100) %></small>
            <% else %>
              <span class="text-muted">-</span>
            <% end %>
          </td>
          <td>
            <div class="btn-group dropstart">
              <button type="button" 
                      class="btn btn-sm btn-light action-btn dropdown-toggle" 
                      data-bs-toggle="dropdown" 
                      aria-expanded="false">
                <i class="fas fa-ellipsis-h"></i>
              </button>
              <ul class="dropdown-menu">
                <li>
                  <%= link_to admin_exam_registration_form_path(e), class: "dropdown-item" do %>
                    <i class="fas fa-eye me-2"></i>Xem chi tiết
                  <% end %>
                </li>
                <% if e.payment_pending? %>
                  <li><hr class="dropdown-divider"></li>
                  <li>
                    <%= link_to mark_as_paid_admin_exam_registration_form_path(e, format: :json), 
                                class: "dropdown-item mark-as-paid-btn",
                                method: :patch,
                                remote: true,
                                data: { 
                                  confirm: "Bạn có chắc chắn muốn đánh dấu đơn này là đã thanh toán không?",
                                  exam_id: e.id,
                                  type: 'json'
                                } do %>
                      <i class="fas fa-check-circle text-success me-2"></i>Đánh dấu đã thanh toán
                    <% end %>
                  </li>
                <% end %>
              </ul>
            </div>
          </td>
        </tr>
      <% end %>
      </tbody>
    </table>
  </div>
  <div class="pagination-section mt-5">
    <%= paginate @exam_registration_forms, theme: 'bootstrap-5',
                 pagination_class: "pagination-sm flex-wrap justify-content-center",
                 nav_class: "d-inline-block" %>
  </div>
</div>
<style>
  table td, table th {
    max-width: 400px;
    min-width: 40px;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: normal;
  }

  .center-filter {
    width: 400px;
  }

  .payment-status-filter {
    width: 250px;
  }

  .pagination-section {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Action button styling */
  .action-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    border: 1px solid #dee2e6;
  }

  .action-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
  }

  /* Remove dropdown arrow */
  .action-btn.dropdown-toggle::after {
    display: none;
  }
</style>
<script type="module">
  $(function () {
    $('#center-filter').select2({
      theme: 'bootstrap',
    });

    $('#payment-status-filter').select2({
      theme: 'bootstrap',
    });

    $('#center-filter, #payment-status-filter').on("change", function () {
      $(this).closest("form").trigger("submit");
    });

    // Handle AJAX response for mark as paid (jquery_ujs style)
    $(document).on('ajax:success', '.mark-as-paid-btn', function(event, data, status, xhr) {
      console.log('AJAX Success:', data, status, xhr.status); // Debug log
      const examId = $(this).data('exam-id');
      
      if (data && data.status === 'success') {
        // Update payment status badge
        const row = $(this).closest('tr');
        const statusCell = row.find('td').eq(25); // Payment status column (index 25)
        statusCell.html('<span class="badge bg-success">Đã thanh toán</span>');
        
        // Update payment note column
        const noteCell = row.find('td').eq(26); // Payment note column (index 26)
        noteCell.html('<small class="text-muted">Admin thay đổi thủ công</small>');
        
        // Remove the mark as paid button
        $(this).closest('li').remove();
        
        // Show success message
        showNotification('success', data.message);
      } else {
        showNotification('error', data ? data.message : 'Có lỗi không xác định');
      }
    });

    $(document).on('ajax:error', '.mark-as-paid-btn', function(event, xhr, status, error) {
      console.log('AJAX Error:', xhr.status, xhr.responseText, status, error); // Debug log
      
      let errorMessage = 'Có lỗi xảy ra khi cập nhật trạng thái thanh toán!';
      
      try {
        const response = JSON.parse(xhr.responseText);
        if (response && response.message) {
          errorMessage = response.message;
        }
      } catch (e) {
        // Keep default error message
      }
      
      showNotification('error', errorMessage);
    });

    // Notification function
    function showNotification(type, message) {
      const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
      const notification = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
          ${message}
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      `;
      $('body').append(notification);
      
      // Auto remove after 5 seconds
      setTimeout(function() {
        $('.alert').fadeOut();
      }, 5000);
    }
  });
</script>
