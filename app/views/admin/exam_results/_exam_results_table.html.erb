<div class="table-responsive">
  <table class="table table-bordered">
    <thead>
    <tr>
      <th><PERSON><PERSON> tên</th>
      <th>CCCD</th>
      <th><PERSON><PERSON><PERSON> sinh</th>
      <th>Giớ<PERSON> tính</th>
      <th>Trường/Cơ quan</th>
      <th>Ngày thi</th>
      <th>Đ<PERSON><PERSON> điểm thi</th>
      <th>M<PERSON> học viên</th>
      <th>G/V</th>
      <th>Điểm Listening</th>
      <th>Điểm Reading</th>
      <th>Điểm Speaking 1</th>
      <th><PERSON><PERSON><PERSON><PERSON> Speaking 2</th>
      <th>Tổng điểm</th>
    </tr>
    </thead>
    <tbody>
    <% exam_results.each do |result| %>
      <tr>
        <td><%= result.student_name %></td>
        <td><%= result.identification_number %></td>
        <td><%= result.dob&.strftime('%d/%m/%Y') %></td>
        <td><%= result.gender %></td>
        <td><%= result.school_name %></td>
        <td><%= result.test_date&.strftime('%d/%m/%Y') %></td>
        <td><%= result.test_location %></td>
        <td><%= result.student_code %></td>
        <td><%= result.g_v %></td>
        <td><%= result.listening_score %> (<%= result.listening_level %>)</td>
        <td><%= result.reading_score %> (<%= result.reading_level %>)</td>
        <td><%= result.speaking_score_1 %> (<%= result.speaking_level_1 %>)</td>
        <td><%= result.speaking_score_2 %> (<%= result.speaking_level_2 %>)</td>
        <td><%= result.total_score %> (<%= result.overall_level %>)</td>
      </tr>
    <% end %>
    </tbody>
  </table>
</div>
<%= paginate exam_results %> 