document.getElementById('exam-results-table').innerHTML = '<%= j render partial: "exam_results_table", locals: { exam_results: @exam_results } %>';

const searchMessage = document.getElementById('search-message');
if ('<%= @exam_results.any? %>' === 'true') {
  searchMessage.className = 'alert alert-success';
  searchMessage.textContent = 'Tìm kiếm thành công!';
  document.getElementById('search-form').reset();
} else {
  searchMessage.className = 'alert alert-danger';
  searchMessage.textContent = 'Không tìm thấy kết quả nào!';
}
searchMessage.style.display = 'block';

// Ẩn thông báo sau 3 giây
setTimeout(() => {
  searchMessage.style.display = 'none';
}, 3000); 