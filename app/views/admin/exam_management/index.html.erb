<div class="container-wrapper">
  <div class="container-lg">
    <div class="row">
      <div class="col-md-12">
        <!-- Flash Messages -->
        <div id="flash-messages"></div>
        
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0">
              <i class="fas fa-sitemap me-2"></i>Quản lý Thi cử Tổng hợp
            </h4>
            <div>
              <%= link_to "Quay lại Dashboard", admin_master_data_path, class: "btn btn-outline-secondary me-2" %>
              <button type="button" class="btn btn-success" id="add-center-btn">
                <i class="fas fa-plus me-2"></i>Thêm Trung tâm thi
              </button>
            </div>
          </div>
          <div class="card-body">
            <div class="alert alert-info mb-4">
              <i class="fas fa-info-circle me-2"></i>
              Quản lý toàn bộ trung tâm thi, ngày thi và ca thi trong một màn hình. Click để mở rộng từng mục.
            </div>
            
            <div id="exam-centers-container">
              <% if @exam_centers.empty? %>
                <div class="text-center text-muted py-5" id="empty-state">
                  <i class="fas fa-building fa-3x mb-3"></i>
                  <h5>Chưa có trung tâm thi nào</h5>
                  <p>Bấm "Thêm Trung tâm thi" để bắt đầu</p>
                </div>
              <% else %>
                <% @exam_centers.each do |center| %>
                  <%= render 'exam_center_item', center: center %>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Center Modal -->
<div class="modal fade" id="addCenterModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Thêm Trung tâm thi</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="add-center-form">
          <div class="mb-3">
            <label class="form-label">Địa chỉ trung tâm thi</label>
            <input type="text" class="form-control" name="address" required>
          </div>
          <div class="mb-3">
            <label class="form-label">Thứ tự hiển thị</label>
            <input type="number" class="form-control" name="position" min="0">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-primary" id="save-center-btn">Lưu</button>
      </div>
    </div>
  </div>
</div>

<!-- Add Day Modal -->
<div class="modal fade" id="addDayModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Thêm Ngày thi</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="add-day-form">
          <input type="hidden" name="exam_center_id" id="day-center-id">
          <div class="mb-3">
            <label class="form-label">Ngày thi</label>
            <input type="date" class="form-control" name="date" required>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-primary" id="save-day-btn">Lưu</button>
      </div>
    </div>
  </div>
</div>

<!-- Add Session Modal -->
<div class="modal fade" id="addSessionModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Thêm Ca thi</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="add-session-form">
          <input type="hidden" name="exam_day_id" id="session-day-id">
          <div class="mb-3">
            <label class="form-label">Ca thi</label>
            <input type="text" class="form-control" name="time_range" 
                   placeholder="HH:MM - HH:MM (ví dụ: 08:00 - 10:00)" required>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-primary" id="save-session-btn">Lưu</button>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const flashMessages = document.getElementById('flash-messages');
  const examCentersContainer = document.getElementById('exam-centers-container');
  const emptyState = document.getElementById('empty-state');
  
  // Modal instances
  const addCenterModal = new bootstrap.Modal(document.getElementById('addCenterModal'));
  const addDayModal = new bootstrap.Modal(document.getElementById('addDayModal'));
  const addSessionModal = new bootstrap.Modal(document.getElementById('addSessionModal'));
  
  // CSRF Token
  const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
  
  // Utility functions
  function showMessage(message, type = 'success') {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const iconClass = type === 'success' ? 'check-circle' : 'exclamation-circle';
    
    flashMessages.innerHTML = `
      <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
        <i class="fas fa-${iconClass} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    `;
    
    // Auto dismiss
    setTimeout(() => {
      const alert = flashMessages.querySelector('.alert');
      if (alert) {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
      }
    }, 5000);
  }
  
  function makeRequest(url, options = {}) {
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'X-Requested-With': 'XMLHttpRequest'
      }
    };
    
    return fetch(url, { ...defaultOptions, ...options })
      .then(response => response.json());
  }
  
  // Toggle center content
  document.addEventListener('click', function(e) {
    if (e.target.closest('.center-toggle')) {
      const centerItem = e.target.closest('.exam-center-item');
      const content = centerItem.querySelector('.center-content');
      const icon = e.target.closest('.center-toggle').querySelector('i');
      
      content.classList.toggle('collapsed');
      icon.classList.toggle('fa-chevron-down');
      icon.classList.toggle('fa-chevron-right');
    }
  });
  
  // Add center
  document.getElementById('add-center-btn').addEventListener('click', function() {
    document.getElementById('add-center-form').reset();
    addCenterModal.show();
  });
  
  document.getElementById('save-center-btn').addEventListener('click', function() {
    const form = document.getElementById('add-center-form');
    const formData = new FormData(form);
    
         makeRequest('/admin/exam_management/create_center', {
      method: 'POST',
      body: JSON.stringify(Object.fromEntries(formData)),
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken
      }
    })
    .then(data => {
      if (data.success) {
        showMessage(data.message);
        addCenterModal.hide();
        location.reload(); // Simple reload for now
      } else {
        showMessage(data.errors.join(', '), 'error');
      }
    })
    .catch(() => {
      showMessage('Có lỗi xảy ra!', 'error');
    });
  });
  
  // Add day
  document.addEventListener('click', function(e) {
    if (e.target.closest('.add-day-btn')) {
      const centerId = e.target.closest('.add-day-btn').dataset.centerId;
      document.getElementById('day-center-id').value = centerId;
      document.getElementById('add-day-form').reset();
      document.getElementById('day-center-id').value = centerId; // Reset after form reset
      addDayModal.show();
    }
  });
  
  document.getElementById('save-day-btn').addEventListener('click', function() {
    const form = document.getElementById('add-day-form');
    const formData = new FormData(form);
    
         makeRequest('/admin/exam_management/create_day', {
      method: 'POST',
      body: JSON.stringify(Object.fromEntries(formData)),
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken
      }
    })
    .then(data => {
      if (data.success) {
        showMessage(data.message);
        addDayModal.hide();
        location.reload(); // Simple reload for now
      } else {
        showMessage(data.errors.join(', '), 'error');
      }
    })
    .catch(() => {
      showMessage('Có lỗi xảy ra!', 'error');
    });
  });
  
  // Add session
  document.addEventListener('click', function(e) {
    if (e.target.closest('.add-session-btn')) {
      const dayId = e.target.closest('.add-session-btn').dataset.dayId;
      document.getElementById('session-day-id').value = dayId;
      document.getElementById('add-session-form').reset();
      document.getElementById('session-day-id').value = dayId; // Reset after form reset
      addSessionModal.show();
    }
  });
  
  document.getElementById('save-session-btn').addEventListener('click', function() {
    const form = document.getElementById('add-session-form');
    const formData = new FormData(form);
    
         makeRequest('/admin/exam_management/create_session', {
      method: 'POST',
      body: JSON.stringify(Object.fromEntries(formData)),
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken
      }
    })
    .then(data => {
      if (data.success) {
        showMessage(data.message);
        addSessionModal.hide();
        location.reload(); // Simple reload for now
      } else {
        showMessage(data.errors.join(', '), 'error');
      }
    })
    .catch(() => {
      showMessage('Có lỗi xảy ra!', 'error');
    });
  });
  
  // Delete handlers
  document.addEventListener('click', function(e) {
    if (e.target.closest('.delete-center-btn')) {
      if (confirm('Bạn có chắc chắn muốn xóa trung tâm thi này? Tất cả ngày thi và ca thi sẽ bị xóa.')) {
        const centerId = e.target.closest('.delete-center-btn').dataset.centerId;
                 makeRequest(`/admin/exam_management/${centerId}/destroy_center`, {
          method: 'DELETE'
        })
        .then(data => {
          if (data.success) {
            showMessage(data.message);
            location.reload();
          } else {
            showMessage(data.message, 'error');
          }
        });
      }
    }
    
    if (e.target.closest('.delete-day-btn')) {
      if (confirm('Bạn có chắc chắn muốn xóa ngày thi này? Tất cả ca thi sẽ bị xóa.')) {
        const dayId = e.target.closest('.delete-day-btn').dataset.dayId;
                 makeRequest(`/admin/exam_management/destroy_day/${dayId}`, {
          method: 'DELETE'
        })
        .then(data => {
          if (data.success) {
            showMessage(data.message);
            location.reload();
          } else {
            showMessage(data.message, 'error');
          }
        });
      }
    }
    
    if (e.target.closest('.delete-session-btn')) {
      if (confirm('Bạn có chắc chắn muốn xóa ca thi này?')) {
        const sessionId = e.target.closest('.delete-session-btn').dataset.sessionId;
                 makeRequest(`/admin/exam_management/destroy_session/${sessionId}`, {
          method: 'DELETE'
        })
        .then(data => {
          if (data.success) {
            showMessage(data.message);
            location.reload();
          } else {
            showMessage(data.message, 'error');
          }
        });
      }
    }
  });
});
</script>

<style>
  .container-wrapper {
    background-color: #F8F8F8;
    padding: 20px 0;
    min-height: 100vh;
  }

  .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }

  .exam-center-item {
    margin-bottom: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    background: white;
    transition: all 0.3s ease;
  }

  .exam-center-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .center-header {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .center-header h5 {
    margin: 0;
    display: flex;
    align-items: center;
  }

  .center-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
  }

  .center-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .center-content {
    padding: 1.5rem;
    max-height: 1000px;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .center-content.collapsed {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }

  .center-actions {
    display: flex;
    gap: 0.5rem;
  }

  .days-section {
    margin-top: 1rem;
  }

  .exam-day-item {
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    background: #f8f9fa;
  }

  .day-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .day-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .sessions-list {
    margin-top: 1rem;
    padding-left: 1.5rem;
  }

  .session-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
  }

  .session-item:hover {
    background: #f1f3f4;
    transform: translateX(2px);
  }

  .session-time {
    font-family: 'Courier New', monospace;
    font-weight: 500;
    color: #495057;
  }

  .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }

  .empty-sessions {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 1rem;
    background: white;
    border: 1px dashed #dee2e6;
    border-radius: 0.25rem;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
  }

  .section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .modal-body .form-label {
    font-weight: 600;
    color: #495057;
  }
</style> 