<div class="exam-center-item" data-center-id="<%= center.id %>">
  <div class="center-header">
    <h5>
      <i class="fas fa-building me-2"></i>
      <%= center.address %>
      <span class="badge bg-light text-dark ms-2">
        <%= pluralize(center.exam_days.count, 'ngày thi') %>
      </span>
    </h5>
    
    <div class="d-flex align-items-center gap-2">
      <div class="center-actions">
        <button type="button" class="btn btn-sm btn-outline-light" title="Chỉnh sửa">
          <i class="fas fa-edit"></i>
        </button>
        <button type="button" class="btn btn-sm btn-outline-light delete-center-btn" 
                data-center-id="<%= center.id %>" title="Xóa">
          <i class="fas fa-trash"></i>
        </button>
      </div>
      <button type="button" class="center-toggle">
        <i class="fas fa-chevron-down"></i>
      </button>
    </div>
  </div>
  
  <div class="center-content">
    <!-- Days Section -->
    <div class="days-section">
      <div class="section-header">
        <h6 class="section-title">
          <i class="fas fa-calendar-alt"></i>
          Ngày thi
        </h6>
        <button type="button" class="btn btn-sm btn-outline-primary add-day-btn" 
                data-center-id="<%= center.id %>">
          <i class="fas fa-plus me-1"></i>Thêm ngày thi
        </button>
      </div>
      
      <% if center.exam_days.empty? %>
        <div class="empty-sessions">
          <i class="fas fa-calendar-plus fa-2x mb-2"></i>
          <p>Chưa có ngày thi nào</p>
          <small>Bấm "Thêm ngày thi" để bắt đầu</small>
        </div>
      <% else %>
        <% center.exam_days.order(:date).each do |day| %>
          <div class="exam-day-item" data-day-id="<%= day.id %>">
            <div class="day-header d-flex justify-content-between align-items-center">
              <div class="day-info">
                <i class="fas fa-calendar-day text-primary"></i>
                <strong><%= day.date.strftime('%d/%m/%Y') %></strong>
                <span class="badge bg-secondary ms-2">
                  <%= pluralize(day.exam_sessions.count, 'ca thi') %>
                </span>
              </div>
              <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-danger delete-day-btn" 
                        data-day-id="<%= day.id %>" title="Xóa ngày thi">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
            
            <!-- Sessions Section -->
            <div class="sessions-list">
              <div class="section-header">
                <h6 class="section-title">
                  <i class="fas fa-clock"></i>
                  Ca thi
                </h6>
                <button type="button" class="btn btn-sm btn-outline-success add-session-btn" 
                        data-day-id="<%= day.id %>">
                  <i class="fas fa-plus me-1"></i>Thêm ca thi
                </button>
              </div>
              
              <% if day.exam_sessions.empty? %>
                <div class="empty-sessions">
                  <i class="fas fa-clock fa-2x mb-2"></i>
                  <p>Chưa có ca thi nào</p>
                  <small>Bấm "Thêm ca thi" để bắt đầu</small>
                </div>
              <% else %>
                <% day.exam_sessions.order(:time_range).each do |session| %>
                  <div class="session-item" data-session-id="<%= session.id %>">
                    <div class="session-info">
                      <i class="fas fa-clock text-success me-2"></i>
                      <span class="session-time"><%= session.time_range %></span>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-session-btn" 
                            data-session-id="<%= session.id %>" title="Xóa ca thi">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                <% end %>
              <% end %>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>
</div> 