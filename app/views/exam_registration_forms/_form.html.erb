<div class="rsform"
     data-controller="exam-registration-forms"
     data-exam-registration-forms-master-colleges-value="<%= @master_colleges.to_json %>"
     data-exam-registration-forms-master-exam-centers-value="<%= @master_exam_centers.to_json %>"
     data-exam-registration-forms-master-exam-days-value="<%= @master_exam_days.to_json %>"
     data-exam-registration-forms-master-exam-sessions-value="<%= @master_exam_sessions.to_json %>"
     data-exam-registration-forms-master-id-document-types-value="<%= @master_id_document_types.to_json %>"
     data-exam-registration-forms-master-registration-reasons-value="<%= @master_registration_reasons.to_json %>"
     data-exam-registration-forms-master-support-choices-value="<%= @master_support_choices.to_json %>"
     data-exam-registration-forms-master-certificate-delivery-methods-value="<%= @master_certificate_delivery_methods.to_json %>"
     <% if Rails.env.development? && @test_data %>data-exam-registration-forms-test-data-value="<%= @test_data.to_json %>"<% end %>
>
  <%= form_with url: exam_registration_forms_path, method: :post, id: "userForm", class: "FormRegistration", local: false, multipart: true, html: { data: { "exam-registration-forms-target": "form", action: "exam-registration-forms#submit" } } do |f| %>
    <fieldset class="formHorizontal formContainer" id="rsform_9_page_0">
      <div class="rsform-block rsform-block-ho">
        <div class="formControlLabel">Họ<strong class="formRequired">(*)</strong><span class="subtitlee">(bao gồm họ và tên đệm)</span>
        </div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="YES" type="text" value="" size="50" name="exam_registration_form[lastname]" id="Ho" placeholder="VD NGUYEN THI VAN" class="rsform-input-box"><span class="formValidation"><span id="component80" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription">(bao gồm họ và tên đệm)</p>
        </div>
      </div>
      <div class="rsform-block rsform-block-ten">
        <div class="formControlLabel">Tên<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="YES" type="text" value="" size="50" name="exam_registration_form[firstname]" id="Ten" placeholder="VD: ANH" class="rsform-input-box"><span class="formValidation"><span id="component81" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-ngaysinh" data-exam-registration-forms-target="birthDay">
        <div class="formControlLabel" style="width: 110px;">Ngày sinh<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <select name="exam_registration_form[birth_day]" id="NgaySinh" placeholder="Ngày" class="rsform-select-box" style="display:none!important;"></select>
            <div class="DropDown">
              <div class="DropDownInput">
                <input type="text" readonly="" placeholder="Ngày">
                <span></span>
              </div>
              <div class="DropDownList">
                <div class="DropDownListInput"><input type="text" placeholder="Ngày"></div>
                <ul>
                </ul>
              </div>
            </div>
            <span class="formValidation"><span id="component82" class="formNoError">Invalid Input</span></span></div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-thangsinh" data-exam-registration-forms-target="birthMonth">
        <div class="formControlLabel">Tháng<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <select name="exam_registration_form[birth_month]" id="ThangSinh" placeholder="Tháng" class="rsform-select-box" style="display:none!important;"></select>
            <div class="DropDown">
              <div class="DropDownInput">
                <input type="text" readonly="" placeholder="Tháng">
                <span></span>
              </div>
              <div class="DropDownList">
                <div class="DropDownListInput"><input type="text" placeholder="Tháng"></div>
                <ul>
                </ul>
              </div>
            </div>
            <span class="formValidation"><span id="component86" class="formNoError">Invalid Input</span></span></div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-namsinh" data-exam-registration-forms-target="birthYear">
        <div class="formControlLabel">Năm<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <select name="exam_registration_form[birth_year]" id="NamSinh" placeholder="Năm" class="rsform-select-box" style="display:none!important;"></select>
            <div class="DropDown">
              <div class="DropDownInput">
                <input type="text" readonly="" placeholder="Năm">
                <span></span>
              </div>
              <div class="DropDownList">
                <div class="DropDownListInput"><input type="text" placeholder="Năm"></div>
                <ul>
                </ul>
              </div>
            </div>
            <span class="formValidation"><span id="component87" class="formNoError">Invalid Input</span></span></div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-gioitinh">
        <div class="formControlLabel">Giới tính<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <input name="exam_registration_form[gender]" type="radio" value="1" id="GioiTinh0"><label for="GioiTinh0">Nam</label><input name="exam_registration_form[gender]" type="radio" value="2" id="GioiTinh1"><label for="GioiTinh1">Nữ</label><span class="formValidation"><span id="component90" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-diadiemthi" data-exam-registration-forms-target="examCenter">
        <div class="formControlLabel">Địa điểm thi<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <select name="exam_registration_form[exam_center_name]" id="DiaDiemThi" placeholder="Chọn địa điểm thi" class="rsform-select-box rsform-error" style="display:none!important;"></select>
            <div class="DropDown">
              <div class="DropDownInput">
                <input type="text" readonly="" placeholder="Chọn địa điểm thi">
                <span></span>
              </div>
              <div class="DropDownList">
                <div class="DropDownListInput"><input type="text" placeholder="Chọn địa điểm thi"></div>
                <ul>
                </ul>
              </div>
            </div>
            <span class="formValidation"><span id="component84" class="formNoError">Invalid Input</span></span></div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-ngaythi" data-exam-registration-forms-target="examDay">
        <div class="formControlLabel">Ngày thi<strong class="formRequired">(*)</strong><span class="subtitlee"></span>
        </div>
        <div class="formControls">
          <div class="formBody">
            <select name="exam_registration_form[exam_day]" id="NgayThi" placeholder="Chọn ngày thi" class="rsform-select-box" style="display:none!important;"></select>
            <div class="DropDown">
              <div class="DropDownInput">
                <input type="text" readonly="" placeholder="Chọn ngày thi">
                <span></span>
              </div>
              <div class="DropDownList">
                <div class="DropDownListInput"><input type="text" placeholder="Chọn ngày thi"></div>
                <ul>
                </ul>
              </div>
            </div>
            <span class="formValidation"><span id="component85" class="formNoError">Invalid Input</span></span></div>
          <p class="formDescription">
            (Lịch thi có thể thay đổi tùy thuộc vào số lượng thí sinh đăng ký)</p>
        </div>
      </div>
      <div class="rsform-block rsform-block-cathi" data-exam-registration-forms-target="examSession">
        <div class="formControlLabel">Ca Thi<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <select name="exam_registration_form[exam_time_range]" id="CaThi" placeholder="Chọn ca thi" class="rsform-select-box" style="display:none!important;"></select>
            <div class="DropDown">
              <div class="DropDownInput">
                <input type="text" readonly="" placeholder="Chọn ca thi">
                <span></span>
              </div>
              <div class="DropDownList">
                <div class="DropDownListInput"><input type="text" placeholder="Chọn ca thi"></div>
                <ul>
                </ul>
              </div>
            </div>
            <span class="formValidation"><span id="component107" class="formNoError">Invalid Input</span></span></div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-capdodangki">
        <div class="formControlLabel">Cấp độ đăng kí dự thi<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <input class="form-check-input" name="exam_registration_form[exam_level]" type="radio" value="A2" id="CapDoA2"><label class="form-check-label" for="CapDoA2">A2</label>
            <input class="form-check-input" name="exam_registration_form[exam_level]" type="radio" value="B1" id="CapDoB1"><label class="form-check-label" for="CapDoB1">B1</label>
            <input class="form-check-input" name="exam_registration_form[exam_level]" type="radio" value="B2" id="CapDoB2"><label class="form-check-label" for="CapDoB2">B2</label>
            <input class="form-check-input" name="exam_registration_form[exam_level]" type="radio" value="C1" id="CapDoC1"><label class="form-check-label" for="CapDoC1">C1</label>
            <span class="formValidation"><span id="component_exam_level" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-donvicongtac">
        <div class="formControlLabel">Bạn đang công tác tại?<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <input name="exam_registration_form[work_location_type]" type="radio" value="1" id="DonViCongTac0" checked="checked"><label for="DonViCongTac0">Học
            viện/trường ĐH,
            CĐ</label><input name="exam_registration_form[work_location_type]" type="radio" value="2" id="DonViCongTac1"><label for="DonViCongTac1">Công
            ty, đơn vị
            khác</label><span class="formValidation"><span id="component75" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-truongdangcongtac" data-exam-registration-forms-target="college">
        <div class="formControlLabel">Chọn học viện/trường đang học tập, công tác</div>
        <div class="formControls">
          <div class="formBody">
            <select name="exam_registration_form[college_name]" id="TruongDangCongTac" placeholder="Chọn học viện/trường đang học tập, công tác" class="rsform-select-box" style="display:none!important;"></select>
            <div class="DropDown">
              <div class="DropDownInput">
                <input type="text" readonly="" placeholder="Chọn học viện/trường đang học tập, công tác">
                <span></span>
              </div>
              <div class="DropDownList">
                <div class="DropDownListInput">
                  <input type="text" placeholder="Chọn học viện/trường đang học tập, công tác"></div>
                <ul>
                </ul>
              </div>
            </div>
            <span class="formValidation"><span id="component77" class="formNoError">Invalid Input</span></span></div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-ctydangcongtac">
        <div class="formControlLabel">Nhập công ty, đơn vị khác</div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="NO" type="text" value="" size="50" name="exam_registration_form[company_name]" id="CtyDangCongTac" placeholder="Nhập công ty, đơn vị khác" class="rsform-input-box"><span class="formValidation"><span id="component79" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-sodienthoai">
        <div class="formControlLabel">Số điện thoại<strong class="formRequired">(*)</strong><span class="subtitlee">(Nhập chính xác số điện thoại để xác thực thông tin)</span>
        </div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="YES" type="text" value="" size="50" name="exam_registration_form[phone_number]" id="SoDienThoai" class="rsform-input-box"><span class="formValidation"><span id="component88" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription">(Nhập chính xác số điện thoại để xác thực thông tin)</p>
        </div>
      </div>
      <div class="rsform-block rsform-block-email">
        <div class="formControlLabel">Email của thí
          sinh<strong class="formRequired">(*)</strong><span class="subtitlee">(Nhập chính xác email để xác thực thông tin)</span>
        </div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="YES" type="text" value="" size="50" name="exam_registration_form[email]" id="Email" class="rsform-input-box"><span class="formValidation"><span id="component89" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription">(Nhập chính xác email để xác thực thông tin)</p>
        </div>
      </div>
      <div class="rsform-block rsform-block-diachilienhe">
        <div class="formControlLabel">Địa chỉ liên hệ<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="YES" type="text" value="" size="50" name="exam_registration_form[detailed_contact_address]" id="DiaChiLienHe" placeholder="Nhập địa chỉ liên hệ đầy đủ (số nhà, tên đường, phường/xã, quận/huyện, tỉnh/thành phố)" class="rsform-input-box"><span class="formValidation"><span id="component91" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription">Vui lòng nhập địa chỉ liên hệ đầy đủ để chúng tôi có thể liên hệ với bạn</p>
        </div>
      </div>
      <div class="rsform-block rsform-block-giaytotuythan" data-exam-registration-forms-target="identityDocumentType">
        <div class="formControlLabel">Giấy tờ tùy thân<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <select name="exam_registration_form[id_document_type]" id="GiayToTuyThan" placeholder="Giấy tờ tùy thân" class="rsform-select-box" style="display:none!important;"></select>
            <div class="DropDown">
              <div class="DropDownInput">
                <input type="text" readonly="" placeholder="Giấy tờ tùy thân">
                <span></span>
              </div>
              <div class="DropDownList">
                <div class="DropDownListInput"><input type="text" placeholder="Giấy tờ tùy thân"></div>
                <ul>
                </ul>
              </div>
            </div>
            <span class="formValidation"><span id="component92" class="formNoError">Invalid Input</span></span></div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-sogiayto">
        <div class="formControlLabel">CMND/CCCD/Passport</div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="NO" type="text" value="" size="50" name="exam_registration_form[id_document_number]" id="SoGiayTo" placeholder="Nhập số CMND/CCCD/Passport" class="rsform-input-box"><span class="formValidation"><span id="component93" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-ngayhethan">
        <div class="formControlLabel">Ngày hết hạn<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="YES" type="date" size="50" name="exam_registration_form[id_expiry_date]" id="NgayHetHan" placeholder="Ngày hết hạn" class="rsform-input-box"><span class="formValidation"><span id="component102" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-noicap">
        <div class="formControlLabel">Nơi cấp<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="YES" type="text" value="" size="50" name="exam_registration_form[id_issue_place]" id="NoiCap" placeholder="Nơi cấp" class="rsform-input-box"><span class="formValidation"><span id="component95" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-anhtruoc">
        <div class="formControlLabel">Ảnh mặt TRƯỚC thẻ CCCD<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="YES" type="text" value="" size="50" id="AnhTruoc" class="rsform-input-box"><span class="fileToUpload"><i class="fa fa-address-card" aria-hidden="true" style="color: rgb(0, 92, 185); font-size: 80px;"></i></span><input class="fileToUploadMain" name="exam_registration_form[id_front_image]" data-id="Truoc" type="file" accept="image/x-png, image/gif, image/jpeg"><span class="formValidation"><span id="component103" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-anhsau">
        <div class="formControlLabel">Ảnh mặt SAU thẻ CCCD<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="YES" type="text" value="" size="50" id="AnhSau" class="rsform-input-box"><span class="fileToUpload"><i class="fas fa-image" aria-hidden="true" style="color: rgb(0, 92, 185); font-size: 80px;"></i></span><input class="fileToUploadMain" name="exam_registration_form[id_back_image]" data-id="Sau" type="file" accept="image/x-png, image/gif, image/jpeg"><span class="formValidation"><span id="component104" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-anh46">
        <div class="formControlLabel">Ảnh chân dung 4x6<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <input data-required="YES" type="text" value="" size="50" name="exam_registration_form[profile_image]" id="Anh46" class="rsform-input-box"><span class="fileToUpload"><i class="fas fa-camera-retro" aria-hidden="true" style="color: rgb(0, 92, 185); font-size: 80px;"></i></span><input class="fileToUploadMain" data-id="ChanDung" name="exam_registration_form[profile_image]" type="file" accept="image/x-png, image/gif, image/jpeg"><span class="formValidation"><span id="component105" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-lydochonthi" data-exam-registration-forms-target="registrationReason">
        <div class="formControlLabel">Lý do bạn đăng ký thi?<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <select name="exam_registration_form[registration_reason]" id="LyDoChonThi" placeholder="Chọn lý do dự thi" class="rsform-select-box" style="display:none!important;"></select>
            <div class="DropDown">
              <div class="DropDownInput">
                <input type="text" readonly="" placeholder="Chọn lý do dự thi">
                <span></span>
              </div>
              <div class="DropDownList">
                <div class="DropDownListInput"><input type="text" placeholder="Chọn lý do dự thi"></div>
                <ul>
                </ul>
              </div>
            </div>
            <span class="formValidation"><span id="component98" class="formNoError">Invalid Input</span></span></div>
          <p class="formDescription"></p>
        </div>
        <div class="formControls mt-2">
          <div class="formBody">
            <input data-required="NO" type="text" value="" size="50" name="exam_registration_form[other_registration_reason]" id="LyDoKhac" placeholder="Mô tả lý do khác mà bạn đăng ký thi" class="rsform-input-box"><span class="formValidation"><span id="component108" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription">Vui lòng mô tả chi tiết lý do khác ngoài các lựa chọn sẵn có để chúng tôi hiểu rõ hơn về mục đích của bạn</p>
        </div>
      </div>
      <div class="rsform-block rsform-block-phuongthucnhanchungchi" data-exam-registration-forms-target="certificateDeliveryMethod">
        <div class="formControlLabel">Phương thức nhận chứng chỉ<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <select name="exam_registration_form[certificate_delivery_method]" id="PhuongThucNhanChungChi" placeholder="Chọn phương thức nhận chứng chỉ" class="rsform-select-box" style="display:none!important;"></select>
            <div class="DropDown">
              <div class="DropDownInput">
                <input type="text" readonly="" placeholder="Chọn phương thức nhận chứng chỉ">
                <span></span>
              </div>
              <div class="DropDownList">
                <div class="DropDownListInput"><input type="text" placeholder="Chọn phương thức nhận chứng chỉ"></div>
                <ul>
                </ul>
              </div>
            </div>
            <span class="formValidation"><span id="component98" class="formNoError">Invalid Input</span></span></div>
          <p class="formDescription"></p>
        </div>
        <div class="formControls mt-2">
          <div class="formBody">
            <input data-required="NO" type="text" value="" size="50" name="exam_registration_form[delivery_address]" id="DiaChiChuyenPhat" placeholder="Nhập địa chỉ đầy đủ để chuyển phát chứng chỉ" class="rsform-input-box"><span class="formValidation"><span id="component106" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription">Vui lòng nhập địa chỉ đầy đủ và chính xác để đảm bảo chứng chỉ được giao đến đúng nơi</p>
        </div>
      </div>
      <div class="rsform-block rsform-block-gapkhokhan mb-0 pb-0">
        <div class="formControlLabel">Bạn có khó khăn về khuyết tật và cần sự hỗ trợ đặc biệt?</div>
        <div class="formControls">
          <div class="formBody">
            <input name="exam_registration_form[need_specific_support]" type="checkbox" value="true" id="GapKhoKhan0"><label for="GapKhoKhan0">Bạn
            có khó khăn về khuyết tật và cần sự hỗ trợ đặc
            biệt?</label><span class="formValidation"><span id="component99" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <div class="rsform-block rsform-block-hotrokhokhan" data-exam-registration-forms-target="supportChoice">
        <div class="formControlLabel" style="visibility: unset;">
          <span class="subtitlee">Các yêu cầu hỗ trợ đặc biệt cần được nộp trước ngày thi từ sáu đến tám tuần. Vui lòng đọc thêm thông tin về Hỗ trợ đặc biệt cho thí sinh trong kỳ thi NOCN trước khi hoàn tất đăng ký.</span>
        </div>
        <div class="formControls">
          <div class="formBody">
            <select name="exam_registration_form[support_choice]" id="HoTroKhoKhan" placeholder="Vui lòng lựa chọn hình thức hỗ trợ" class="rsform-select-box" style="display:none!important;"></select>
            <div class="DropDown">
              <div class="DropDownInput">
                <input type="text" readonly="" placeholder="Vui lòng lựa chọn hình thức hỗ trợ">
                <span></span>
              </div>
              <div class="DropDownList">
                <div class="DropDownListInput"><input type="text" placeholder="Vui lòng lựa chọn hình thức hỗ trợ">
                </div>
                <ul style="max-height: 150px">
                </ul>
              </div>
            </div>
            <span class="formValidation"><span id="component100" class="formNoError">Invalid Input</span></span></div>
        </div>
        <div class="formControls mt-2">
          <div class="formBody">
            <input data-required="NO" type="text" value="" size="50" name="exam_registration_form[other_difficulty]" id="KhoKhanKhac" placeholder="Mô tả khó khăn khác mà bạn gặp phải" class="rsform-input-box"><span class="formValidation"><span id="component107" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription">Vui lòng mô tả chi tiết những khó khăn khác ngoài các lựa chọn sẵn có để chúng tôi có thể hỗ trợ bạn tốt nhất</p>
        </div>
      </div>
      <div class="rsform-block rsform-block-xacnhan">
        <div class="formControlLabel">Tôi xác nhận<strong class="formRequired">(*)</strong></div>
        <div class="formControls">
          <div class="formBody">
            <input name="confirm" type="checkbox" value="Tôi xác nhận mọi thông tin cá nhân (họ & tên, ngày sinh, giấy tờ tùy thân, địa chỉ…) đã được điền đầy đủ và chính xác; những thông tin này sẽ được sử dụng để đăng ký dự thi và hiển thị trên Giấy chứng nhận ESOL International. Tôi hiểu rằng để được phép tham dự kỳ thi, tôi phải xuất trình giấy tờ tùy thân gốc hợp lệ (hộ chiếu hoặc Chứng minh nhân dân/Thẻ Căn cước Công dân còn hiệu lực); việc không xuất trình được giấy tờ phù hợp sẽ dẫn đến việc tôi không được tham dự thi và không được hoàn trả lệ phí đăng ký dự thi." id="XacNhan0"><label for="XacNhan0">
            Tôi xác nhận mọi thông tin cá nhân (họ & tên, ngày sinh, giấy tờ tùy thân, địa chỉ…) đã được điền đầy đủ và chính xác; những thông tin này sẽ được sử dụng để đăng ký dự thi và hiển thị trên Giấy chứng nhận ESOL International. Tôi hiểu rằng để được phép tham dự kỳ thi, tôi phải xuất trình giấy tờ tùy thân gốc hợp lệ (hộ chiếu hoặc Chứng minh nhân dân/Thẻ Căn cước Công dân còn hiệu lực); việc không xuất trình được giấy tờ phù hợp sẽ dẫn đến việc tôi không được tham dự thi và không được hoàn trả lệ phí đăng ký dự thi.
            </label><span class="formValidation"><span id="component101" class="formNoError">Invalid Input</span></span>
          </div>
          <p class="formDescription"></p>
        </div>
      </div>
      <% if Rails.env.development? && @test_data %>
      <div class="rsform-submit mb-3">
        <button type="button" class="btn btn-warning" data-action="click->exam-registration-forms#fillTestData" style="background-color: #ffc107; border-color: #ffc107; color: #212529; font-weight: bold;">
          🧪 Fill Test Data (Dev Only)
        </button>
      </div>
      <% end %>
      <div class="rsform-submit">
        <input type="submit" value="Đăng ký dự thi" id="submitForm" data-exam-registration-forms-target="submitButton">
      </div>
    </fieldset>
  <% end %>
  <div class="col-md-12 timeline thankyou-section" style="margin: 0px auto;" data-exam-registration-forms-target="thankYou">
    <div class="thankyou pb-2">
      <h3 class="text-center" style="margin-bottom: 0px;">Thông tin đăng ký đã gửi tới Email của bạn!</h3>
      <div class="col-sm-12 col-sm-offset-3 bg-white" style="padding: 0 15px; border: 1px solid rgb(204, 204, 204); border-bottom-right-radius: 5px; border-bottom-left-radius: 5px;">
        <p class="pt-2" style="color: red; font-weight: bold;">CHÚC MỪNG BẠN ĐÃ ĐĂNG KÝ DỰ THI THÀNH CÔNG!</p>
        <div><strong>CÔNG TY TNHH SEA EDUCATION TRAINING</strong><br>
          Số điện thoại hỗ trợ:&nbsp;+84 886681666<br>
          Email:&nbsp;<EMAIL><br>
          Địa chỉ:&nbsp;26 Phố Đinh Núp, Phường Trung Hòa, Quận Cầu Giấy, Tp Hà Nội
          <hr>
          <strong>*Thí sinh lưu ý:&nbsp;</strong><br>
          <strong>Về thông tin đăng ký:</strong><br>
          <em>- Thông tin đăng ký sẽ được dùng để đăng ký thi và hiển thị trên<strong> chứng chỉ;</strong></em><br>
          <em>-&nbsp;Thí sinh sẽ không được chấp nhận vào phòng thi và không được hoàn trả lệ phí thi nếu không thể xuất
            trình giấy tờ tùy thân đúng với thông tin đã đăng ký và hợp lệ
            theo<a href="https://www.britishcouncil.vn/sites/default/files/policy-about-presenting-identification-document-for-nocn-vie.pdf" target="_blank">&nbsp;Chính
            sách về Giấy tờ tùy thân</a>&nbsp;của Hội đồng Anh.</em><br>
          <strong>Về thanh toán lệ phí thi:</strong><br>
          <em>- Đảm bảo nội dung chuyển khoản trùng khớp, chính xác để xác nhận thanh toán;<br>
            - Trong trường hợp thanh toán thành công, email&nbsp;<strong>[SEA-EDUCATION] Xác nhận nộp lệ phí thi thành
            công&nbsp;</strong>sẽ được gửi trong 01 ngày làm việc (không tính ngày nghỉ và ngày lễ);<br>
            - Nếu quá thời gian trên và thí sinh không nhận được email&nbsp;<strong>[SEA-EDUCATION] Xác nhận nộp lệ phí thi thành
            công&nbsp;</strong>vui lòng liên hệ lại với chúng tôi.</em>
          <hr>
          Để hoàn tất đăng ký dự thi, trong vòng <span style="color:#e74c3c;"><strong>48</strong> </span>giờ tiếp theo,
          bạn vui lòng chuyển lệ phí thi tới thông tin tài khoản sau:<br>
          <br>

          <!-- QR Code Container -->
          <div id="qr-code-container" class="text-center mb-4"></div>
          Tên tài khoản (người thụ hưởng): <b>CÔNG TY TNHH SEA EDUCATION TRAINING</b><br>
          MST: <strong>**********</strong><br>
          Số tài khoản (VND):<strong> 105 800 6666</strong><br>
          Tên ngân hàng: <strong>Ngân hàng Ngoại thương Việt Nam (Vietcombank)</strong><br>
          <span style="color:#e74c3c;"><em>*trong trường hợp nội dung chuyển khoản vượt quá số ký tự cho phép, thí sinh vui lòng thay họ và tên bằng số CCCD/CMND/Hộ chiếu trong hồ sơ đăng ký</em></span>
        </div>
        <div class="clearfix"></div>
        <p>Số tiền:
          <span id="payment-amount" style="color: red; font-weight: bold;"><%= format_currency(1600000) %></span><br>
          Nội dung chuyển khoản: <span id="transfer-content" style="color: red; font-weight: bold;"></span><br>
          Nội dung chuyển khoản trong trường hợp nội dung chuyển khoản vượt quá số ký tự cho phép:
          <span id="transfer-content-long" style="color: red; font-weight: bold;"></span><br>
          <span style="color:#e74c3c;"><em>*không thay đổi nội dung chuyển khoản sẽ dẫn đến việc không thể xác nhận thanh toán</em></span>
        </p>
        <div class="clearfix"></div>
        <p></p>
        <p>Hãy kiểm tra email của bạn (hộp thư hoặc thư rác) để thực hiện các nội dung tiếp theo liên quan đến Kỳ
          thi.</p>
        Thông tin chi tiết, vui lòng liên hệ:<br>
        <i class="fa fa-envelope"></i> <EMAIL><br>
        <i class="fa fa-phone"></i> +84 886681666
        <div class="clearfix"></div>
        <br>
      </div>
    </div>
  </div>
</div>
