<% if user_signed_in? %>
  <aside class="main-sidebar sidebar-dark-primary elevation-4">
    <%= link_to admin_root_path, class: "brand-link" do %>
      <span class="brand-text font-weight-light">SEA EDUCATION ADMIN</span>
    <% end %>

    <div class="sidebar">
      <nav class="mt-2">
        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
          <li class="nav-item">
            <%= link_to admin_root_path, class: "nav-link #{link_state(admin_root_path)}" do %>
              <i class="nav-icon fas fa-tachometer-alt"></i>
              <p>Dashboard</p>
            <% end %>
          </li>

          <li class="nav-item">
            <%= link_to admin_exam_registration_forms_path, class: "nav-link #{link_state(admin_exam_registration_forms_path)}" do %>
              <i class="nav-icon fas fa-clipboard-list"></i>
              <p>Quản lý đăng ký thi</p>
            <% end %>
          </li>

          <li class="nav-item">
            <%= link_to admin_exam_results_path, class: "nav-link #{link_state(admin_exam_results_path)}" do %>
              <i class="nav-icon fas fa-file-alt"></i>
              <p>Quản lý hậu kiểm</p>
            <% end %>
          </li>

          <li class="nav-item">
            <%= link_to admin_contacts_path, class: "nav-link #{link_state(admin_contacts_path)}" do %>
              <i class="nav-icon fas fa-envelope"></i>
              <p>Quản lý liên hệ</p>
            <% end %>
          </li>

          <li class="nav-item">
            <%= link_to admin_bank_transaction_imports_path, class: "nav-link #{link_state(admin_bank_transaction_imports_path)}" do %>
              <i class="nav-icon fas fa-file-import"></i>
              <p>Import Giao dịch</p>
            <% end %>
          </li>

          <li class="nav-item">
            <%= link_to admin_master_data_path, class: "nav-link #{'active' if controller_name.in?(%w[master_data cities colleges identity_document_types registration_reasons support_choices certificate_delivery_methods exam_centers exam_center_visibility exam_days exam_sessions])}" do %>
              <i class="nav-icon fas fa-database"></i>
              <p>Quản lý Dữ liệu</p>
            <% end %>
          </li>
        </ul>
      </nav>
    </div>
  </aside> 
<% end %>
