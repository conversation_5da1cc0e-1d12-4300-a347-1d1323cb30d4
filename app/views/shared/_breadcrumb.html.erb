<div class="breadcrumb">
  <div class="breadcrumb2">
    <div class="breadcrumb2" style="padding-top: 3px; padding-bottom: 0px;">
      <i class="fa fa-home" aria-hidden="true"></i>
      <a href="/" title="Trang chủ">Trang chủ</a>
      <% if @current_page.present? %>
        <i class="fa fa-angle-double-right"></i>
        <a href="<%= @current_page[:url] %>" title="<%= @current_page[:title] %>" style="font-weight: bold;">
          <%= @current_page[:title] %>
        </a>
      <% elsif controller.controller_name != 'home' %>
        <i class="fa fa-angle-double-right"></i>
        <span style="font-weight: bold;">
          <%= controller.controller_name.titleize %>
        </span>
      <% end %>
    </div>
  </div>
</div>

<!-- Breadcrumb Structured Data -->
<% if @current_page.present? %>
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Trang chủ",
      "item": "<%= root_url %>"
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "<%= @current_page[:title] %>",
      "item": "<%= request.original_url %>"
    }
  ]
}
</script>
<% end %>
