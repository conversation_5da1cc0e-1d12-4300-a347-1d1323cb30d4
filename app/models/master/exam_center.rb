# frozen_string_literal: true

module Master
  class ExamCenter < ApplicationRecord
    self.table_name = "exam_centers"
    has_many :exam_days, class_name: Master::ExamDay.name, dependent: :destroy

    # Default scope để tự động sắp xếp theo position và id
    default_scope { order(:position, :id) }

    # Scope để lấy những trung tâm thi hiển thị cho user
    scope :visible, -> { where(is_visible: true) }
    scope :hidden, -> { where(is_visible: false) }

    validates :address, presence: true
    validates :is_visible, inclusion: { in: [true, false] }
    validates :position, presence: true, numericality: { greater_than_or_equal_to: 0 }
    validates :exam_fee, presence: true, numericality: { greater_than: 0 }

    before_create :set_position

    private

    def set_position
      self.position = (self.class.maximum(:position) || 0) + 1
    end
  end
end
