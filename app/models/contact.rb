class Contact < ApplicationRecord
  # Sanitize input tr<PERSON>ớ<PERSON> khi validate
  before_validation :sanitize_inputs

  validates :name, presence: { message: I18n.t('contact.validations.name.blank') },
                   length: {
                     minimum: 2,
                     maximum: 100,
                     too_short: I18n.t('contact.validations.name.too_short'),
                     too_long: I18n.t('contact.validations.name.too_long')
                   }

  validates :email, presence: { message: I18n.t('contact.validations.email.blank') },
                   format: {
                     with: URI::MailTo::EMAIL_REGEXP,
                     message: I18n.t('contact.validations.email.invalid')
                   },
                   length: {
                     maximum: 255,
                     too_long: I18n.t('contact.validations.email.too_long')
                   }

  validates :message, presence: { message: I18n.t('contact.validations.message.blank') },
                     length: {
                       minimum: 10,
                       maximum: 1000,
                       too_short: I18n.t('contact.validations.message.too_short'),
                       too_long: I18n.t('contact.validations.message.too_long')
                     }

  # Scope để tìm kiếm
  scope :recent, -> { order(created_at: :desc) }
  scope :by_email, ->(email) { where(email: email) }

  private

  def sanitize_inputs
    self.name = name&.strip&.squish
    self.email = email&.strip&.downcase
    self.message = message&.strip&.squish
  end
end
