class BankTransactionImport < ApplicationRecord
  validates :file_name, presence: true
  validates :imported_at, presence: true
  validates :total_transactions, :processed_count, :matched_count,
            presence: true, numericality: { greater_than_or_equal_to: 0 }

  scope :recent, -> { order(imported_at: :desc) }
  scope :latest, -> { recent.limit(1) }

  def success_rate
    return 0 if total_transactions.zero?

    (matched_count.to_f / total_transactions * 100).round(2)
  end

  def import_summary
    "#{matched_count}/#{total_transactions} giao dịch được khớp (#{success_rate}%)"
  end
end
