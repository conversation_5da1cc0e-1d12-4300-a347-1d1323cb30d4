class ExamResult < ApplicationRecord
  validates :student_name, presence: true
  validates :identification_number, presence: true
  validates :dob, presence: true
  validates :test_date, presence: true
  validates :student_code, presence: true
  validates :g_v, presence: true

  def self.import(file)
    spreadsheet = Roo::Spreadsheet.open(file.path)

    (2..spreadsheet.last_row).each do |i|
      row = spreadsheet.row(i)

      next if row[2].blank?

      exam_result = find_by(identification_number: row[2]) || new
      exam_result.attributes = {
        student_name: "#{row[0]} #{row[1]}".strip,
        identification_number: row[2],
        dob: row[3],
        gender: row[4],
        school_name: row[5],
        test_date: row[6],
        test_location: row[7],
        student_code: row[8],
        g_v: row[9],
        listening_score: row[10],
        reading_score: row[11],
        speaking_score_1: row[12],
        speaking_score_2: row[13],
        total_score: row[14],
        listening_level: row[15],
        reading_level: row[16],
        speaking_level_1: row[17],
        speaking_level_2: row[18],
        overall_level: row[19]
      }
      exam_result.save!
    end
  end
end
