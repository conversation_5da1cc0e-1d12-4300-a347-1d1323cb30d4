# frozen_string_literal: true

class ExamRegistrationForm < ApplicationRecord
  # belongs_to :exam_session, class_name: Master::ExamSession.name
  # belongs_to :city, class_name: Master::City.name
  # belongs_to :college, class_name: Master::College.name
  self.table_name = 'exam_registration_forms'

  has_one_attached :id_front_image
  has_one_attached :id_back_image
  has_one_attached :profile_image

  enum gender: {
    "Nam": 1,
    "Nữ": 2
  }

  enum work_location_type: {
    "Học viện/Trường ĐH, CĐ": 1,
    "Công ty, đơn vị khác": 2
  }

  enum payment_status: {
    pending: 0,
    paid: 1,
    failed: 2
  }

  validates :email, :phone_number, :firstname, :lastname,
            :birthday, :gender, :work_location_type, :id_document_type,
            :id_document_number, :id_issue_place, :id_expiry_date,
            :college_name, :exam_time_range, :registration_reason, :exam_center_name,
            :exam_day, :certificate_delivery_method, :detailed_contact_address, :exam_level, presence: true
  validates :uuid, presence: true, uniqueness: true

  validate :verify_master_data
  validate :verify_exam_level
  before_validation :generate_uuid
  before_save :normalize_conditional_fields

  def generate_payment_code
    "NOCN #{uuid}"
  end

  def get_exam_center_fee
    exam_center = Master::ExamCenter.visible.find_by(address: exam_center_name)
    exam_center&.exam_fee || 1_600_000.00
  end

  def mark_as_paid!(sepay_transaction_id, amount = nil, note = nil)
    self.payment_status = :paid
    self.sepay_transaction_id = sepay_transaction_id
    self.payment_confirmed_at = Time.current
    self.payment_note = note || "Đã chuyển khoản thành công (#{amount&.to_i&.to_s&.reverse&.gsub(/(\d{3})(?=\d)/, '\\1,')&.reverse} VND)"
    save!(validate: false)
  end

  def mark_as_paid_by_admin!
    self.payment_status = :paid
    self.payment_confirmed_at = Time.current
    self.payment_note = "Admin thay đổi thủ công"
    save!(validate: false)
  end

  def mark_as_insufficient!(amount, required_amount, note = nil)
    self.payment_status = :failed
    self.payment_note = note || "Chuyển khoản không đủ (#{amount&.to_i&.to_s&.reverse&.gsub(/(\d{3})(?=\d)/, '\\1,')&.reverse} VND < #{required_amount&.to_i&.to_s&.reverse&.gsub(/(\d{3})(?=\d)/, '\\1,')&.reverse} VND)"
    save!(validate: false)
  end

  def mark_as_not_found!(note = nil)
    self.payment_status = :failed
    self.payment_note = note || "Không tìm thấy giao dịch"
    save!(validate: false)
  end

  def payment_pending?
    payment_status == 'pending'
  end

  def payment_paid?
    payment_status == 'paid'
  end

  def payment_failed?
    payment_status == 'failed'
  end

  private

  def verify_master_data
    errors.add :college_name, :invalid, message: 'Value is not valid' unless verify_master_college
    errors.add :id_document_type, :invalid, message: 'Value is not valid' unless verify_identity_document_type
    errors.add :registration_reason, :invalid, message: 'Value is not valid' unless verify_registration_reason
    errors.add :support_choice, :invalid, message: 'Value is not valid' unless verify_support_choice
    errors.add :exam_center_name, :invalid, message: 'Value is not valid' unless verify_exam_center
    errors.add :exam_day, :invalid, message: 'Value is not valid' unless verify_exam_day
    errors.add :exam_time_range, :invalid, message: 'Value is not valid' unless verify_exam_session
    return if verify_certificate_delivery_method

    errors.add :certificate_delivery_method, :invalid,
               message: 'Value is not valid'
  end

  def verify_master_college
    Master::College.exists?(name: college_name)
  end

  def verify_identity_document_type
    Master::IdentityDocumentType.exists?(name: id_document_type)
  end

  def verify_registration_reason
    Master::RegistrationReason.exists?(description: registration_reason)
  end

  def verify_support_choice
    return true if support_choice.blank?

    Master::SupportChoice.exists?(name: support_choice)
  end

  def verify_exam_center
    Master::ExamCenter.visible.exists?(address: exam_center_name)
  end

  def verify_exam_day
    exam_center = Master::ExamCenter.visible.find_by(address: exam_center_name)
    return false unless exam_center.present?

    begin
      exam_day_date = Date.strptime(exam_day, '%d/%m/%Y')
    rescue ArgumentError
      return false
    end
    exam_center.exam_days.exists?(date: exam_day_date)
  end

  def verify_exam_session
    return false unless verify_exam_day

    exam_center = Master::ExamCenter.visible.find_by(address: exam_center_name)
    exam_days = exam_center.exam_days.includes(:exam_sessions).find_by(date: Date.strptime(exam_day, '%d/%m/%Y'))
    exam_days.exam_sessions.exists?(time_range: exam_time_range)
  end

  def verify_certificate_delivery_method
    Master::CertificateDeliveryMethod.exists?(name: certificate_delivery_method)
  end

  def verify_exam_level
    valid_levels = %w[A2 B1 B2 C1]
    return if exam_level.blank?

    return if valid_levels.include?(exam_level)

    errors.add :exam_level, :invalid,
               message: "Invalid exam level: #{exam_level}"
  end

  def normalize_conditional_fields
    self.other_registration_reason = nil unless registration_reason_is_other?

    self.delivery_address = nil unless certificate_delivery_method_is_shipping?

    return if support_choice_is_other?

    self.other_difficulty = nil
  end

  def registration_reason_is_other?
    registration_reason.present? && registration_reason == 'Khác, vui lòng nêu rõ (Other please specify)'
  end

  def certificate_delivery_method_is_shipping?
    certificate_delivery_method.present? && certificate_delivery_method == 'express_delivery'
  end

  def support_choice_is_other?
    support_choice.present? && support_choice == 'Khác, vui lòng nêu rõ (Other please specify)'
  end

  def generate_uuid
    return if uuid.present?

    self.uuid = unique_uuid
  end

  def unique_uuid
    loop do
      token = SecureRandom.hex(8)
      break token unless ExamRegistrationForm.exists?(uuid: token)
    end
  end
end
