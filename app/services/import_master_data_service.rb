# frozen_string_literal: true

class ImportMasterDataService
  attr_reader :model, :file_path

  def initialize(model, file_path)
    @model = model
    @file_path = file_path
  end

  def call
    puts "importing #{model}"
    YAML.load_file(file_path).each do |row|
      model.find_or_initialize_by(id: row["id"]).tap do |record|
        record.assign_attributes(row.except("id"))
        record.save
      end
    end
    puts "imported #{model}"
  rescue StandardError => e
    puts e.message
    Rails.logger.error(e.message)
  end
end
