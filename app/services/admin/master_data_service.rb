module Admin
  class MasterDataService
    def self.update_cities(cities_text)
      city_names = parse_text_to_array(cities_text)
      existed_city_names = Master::City.pluck(:name)
      new_city_names = city_names - existed_city_names
      cities_need_to_delete = existed_city_names - city_names

      ActiveRecord::Base.transaction do
        new_city_names.each { |city_name| Master::City.create(name: city_name) }
        Master::City.where(name: cities_need_to_delete).destroy_all
      end
    end

    def self.update_colleges(colleges_text)
      colleges_name = parse_text_to_array(colleges_text)
      existed_colleges_names = Master::College.pluck(:name)
      new_college_names = colleges_name - existed_colleges_names
      colleges_need_to_delete = existed_colleges_names - colleges_name

      ActiveRecord::Base.transaction do
        new_college_names.each { |college_name| Master::College.create(name: college_name) }
        Master::College.where(name: colleges_need_to_delete).destroy_all
      end
    end

    def self.update_identity_document_types(types_text)
      identity_document_types_name = parse_text_to_array(types_text)
      existed_types = Master::IdentityDocumentType.pluck(:name)
      new_types = identity_document_types_name - existed_types
      types_to_delete = existed_types - identity_document_types_name

      ActiveRecord::Base.transaction do
        new_types.each { |type_name| Master::IdentityDocumentType.create(name: type_name) }
        Master::IdentityDocumentType.where(name: types_to_delete).destroy_all
      end
    end

    def self.update_support_choices(choices_text)
      support_choices_name = parse_text_to_array(choices_text)
      existed_choices = Master::SupportChoice.pluck(:name)
      new_choices = support_choices_name - existed_choices
      choices_to_delete = existed_choices - support_choices_name

      ActiveRecord::Base.transaction do
        new_choices.each { |choice_name| Master::SupportChoice.create(name: choice_name) }
        Master::SupportChoice.where(name: choices_to_delete).destroy_all
      end
    end

    def self.update_registration_reasons(reasons_text)
      registration_reasons = parse_text_to_array(reasons_text)
      existed_reasons = Master::RegistrationReason.pluck(:description)
      new_reasons = registration_reasons - existed_reasons
      reasons_to_delete = existed_reasons - registration_reasons

      ActiveRecord::Base.transaction do
        new_reasons.each { |description| Master::RegistrationReason.create(description: description) }
        Master::RegistrationReason.where(description: reasons_to_delete).destroy_all
      end
    end

    def self.update_certificate_delivery_methods(methods_text)
      certificate_delivery_methods = parse_text_to_array(methods_text)
      existed_methods = Master::CertificateDeliveryMethod.pluck(:name)
      new_methods = certificate_delivery_methods - existed_methods
      methods_to_delete = existed_methods - certificate_delivery_methods

      ActiveRecord::Base.transaction do
        new_methods.each { |name| Master::CertificateDeliveryMethod.create(name: name) }
        Master::CertificateDeliveryMethod.where(name: methods_to_delete).destroy_all
      end
    end

    def self.update_exam_centers(centers_text)
      exam_centers_addresses = parse_text_to_array(centers_text)
      existed_addresses = Master::ExamCenter.pluck(:address)
      new_addresses = exam_centers_addresses - existed_addresses
      addresses_to_delete = existed_addresses - exam_centers_addresses

      ActiveRecord::Base.transaction do
        # Thêm trung tâm mới (mặc định is_visible = true)
        new_addresses.each { |address| Master::ExamCenter.create(address: address, is_visible: true) }
        # Xóa trung tâm không còn trong danh sách
        Master::ExamCenter.where(address: addresses_to_delete).destroy_all
      end
    end

    private_class_method def self.parse_text_to_array(text)
      text.split(/\r?\n/).map(&:strip).reject(&:empty?)
    end
  end
end
