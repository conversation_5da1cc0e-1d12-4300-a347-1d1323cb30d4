class BankTransactionImportService
  def initialize(file_path, file_name)
    @file_path = file_path
    @file_name = file_name
    @total_transactions = 0
    @processed_count = 0
    @matched_count = 0
    @import_notes = []
  end

  def process
    import_record = BankTransactionImport.create!(
      file_name: @file_name,
      imported_at: Time.current,
      total_transactions: 0,
      processed_count: 0,
      matched_count: 0
    )

    begin
      process_csv_file

      import_record.update!(
        total_transactions: @total_transactions,
        processed_count: @processed_count,
        matched_count: @matched_count,
        import_notes: @import_notes.join("\n")
      )

      {
        success: true,
        import_record: import_record,
        summary: "Đã xử lý #{@processed_count}/#{@total_transactions} giao dịch, khớp được #{@matched_count} giao dịch"
      }
    rescue StandardError => e
      import_record.update!(
        import_notes: "Lỗi khi xử lý file: #{e.message}"
      )

      {
        success: false,
        error: e.message,
        import_record: import_record
      }
    end
  end

  private

  def process_csv_file
    require 'csv'

    CSV.foreach(@file_path, headers: true, encoding: 'UTF-8') do |row|
      @total_transactions += 1

      # Giả sử CSV có các cột: date, amount, content, type
      # Bạn cần điều chỉnh theo format CSV thực tế
      transaction_data = {
        date: row['date'] || row['Date'] || row['Ngày'],
        amount: parse_amount(row['amount'] || row['Amount'] || row['Số tiền']),
        content: row['content'] || row['Content'] || row['Nội dung'],
        type: row['type'] || row['Type'] || row['Loại']
      }

      next unless transaction_data[:type]&.downcase&.include?('in') ||
                  transaction_data[:type]&.include?('Có') ||
                  transaction_data[:amount].to_f.positive?

      @processed_count += 1

      @matched_count += 1 if process_transaction(transaction_data)
    end
  end

  def process_transaction(transaction_data)
    content = transaction_data[:content]
    amount = transaction_data[:amount].to_f

    return false if content.blank? || amount <= 0

    # Tìm UUID trong nội dung giao dịch
    uuid_match = content.match(/VEPT\s+([a-f0-9\-]{36})/i)
    return false unless uuid_match

    uuid = uuid_match[1]
    exam_form = ExamRegistrationForm.find_by(uuid: uuid, payment_status: :pending)
    return false unless exam_form

    required_amount = exam_form.get_exam_center_fee

    if amount >= required_amount
      exam_form.mark_as_paid!("IMPORT_#{Time.current.to_i}", amount,
                              "Đã chuyển khoản thành công qua import (#{format_currency(amount)} VND)")
      @import_notes << "✓ UUID #{uuid}: Thanh toán thành công #{format_currency(amount)} VND"
    else
      exam_form.mark_as_insufficient!(amount, required_amount,
                                      "Chuyển khoản không đủ qua import (#{format_currency(amount)} VND < #{format_currency(required_amount)} VND)")
      @import_notes << "⚠ UUID #{uuid}: Thanh toán không đủ #{format_currency(amount)} VND (cần #{format_currency(required_amount)} VND)"
    end
    true
  rescue StandardError => e
    @import_notes << "✗ Lỗi xử lý UUID #{uuid}: #{e.message}"
    false
  end

  def parse_amount(amount_str)
    return 0 if amount_str.blank?

    # Loại bỏ các ký tự không phải số và dấu chấm/phẩy
    cleaned = amount_str.to_s.gsub(/[^\d.,\-]/, '')

    # Chuyển đổi dấu phẩy thành dấu chấm nếu cần
    cleaned = cleaned.tr(',', '.') if cleaned.count(',') == 1 && cleaned.count('.').zero?

    cleaned.to_f
  end

  def format_currency(amount)
    amount.to_i.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse
  end
end
