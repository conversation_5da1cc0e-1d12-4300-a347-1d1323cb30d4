module Admin::<PERSON><PERSON><PERSON>el<PERSON>
  def exam_center_visibility_status(exam_center)
    if exam_center.is_visible?
      content_tag :span, class: 'badge bg-success' do
        "#{content_tag(:i, '', class: 'fas fa-eye me-1')}Hiển thị"
      end
    else
      content_tag :span, class: 'badge bg-secondary' do
        "#{content_tag(:i, '', class: 'fas fa-eye-slash me-1')}Ẩn"
      end
    end
  end

  def exam_center_stats(exam_center)
    exam_days_count = exam_center.exam_days.count
    exam_sessions_count = exam_center.exam_days.joins(:exam_sessions).count

    content_tag :small, class: 'text-muted' do
      safe_join([
                  content_tag(:i, '', class: 'fas fa-calendar-alt me-1'),
                  "#{exam_days_count} ngày thi",
                  content_tag(:i, '', class: 'fas fa-clock ms-2 me-1'),
                  "#{exam_sessions_count} ca thi"
                ])
    end
  end
end
