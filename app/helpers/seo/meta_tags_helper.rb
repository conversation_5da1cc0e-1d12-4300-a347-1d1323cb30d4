module Seo
  module <PERSON>a<PERSON>ags<PERSON>elper
    def default_meta_tags
      {
        site: 'SEA Education',
        title: 'SEA Education - Trung tâm thi chứng chỉ tiếng Anh NOCN uy tín',
        description: I18n.t('seo.default.description',
                            default: 'SEA Education là trung tâm thi chứng chỉ tiếng Anh NOCN uy tín tại Việt Nam. ' \
                                     'Đăng ký thi NOCN, tra cứu kết quả thi online, ' \
                                     'nhận chứng chỉ quốc tế được công nhận.'),
        keywords: 'chứng chỉ tiếng anh, NOCN, thi tiếng anh, SEA Education, ' \
                  'chứng chỉ quốc tế, CEFR, đăng ký thi tiếng anh',
        canonical: request.original_url,
        separator: '|',
        reverse: true,
        og: {
          title: :title,
          description: :description,
          type: 'website',
          url: request.original_url,
          image: logo_image_url,
          site_name: 'SEA Education'
        },
        twitter: {
          card: 'summary_large_image',
          site: '@seaeducation',
          title: :title,
          description: :description,
          image: logo_image_url
        }
      }
    end

    def set_meta_tags_for_page(title: nil, description: nil, keywords: nil, image: nil)
      meta_data = default_meta_tags

      meta_data[:title] = title if title.present?
      meta_data[:description] = description if description.present?
      meta_data[:keywords] = keywords if keywords.present?

      if image.present?
        meta_data[:og][:image] = image
        meta_data[:twitter][:image] = image
      end

      set_meta_tags(meta_data)
    end

    def page_title(title = nil)
      if title.present?
        "#{title} | SEA Education"
      else
        "SEA Education - Trung tâm thi chứng chỉ tiếng Anh NOCN uy tín"
      end
    end

    def page_description(description = nil)
      default_desc = I18n.t('seo.default.description',
                            default: 'SEA Education là trung tâm thi chứng chỉ tiếng Anh NOCN uy tín tại Việt Nam. ' \
                                     'Đăng ký thi NOCN, tra cứu kết quả thi online, ' \
                                     'nhận chứng chỉ quốc tế được công nhận.')
      description.presence || default_desc
    end

    private

    def logo_image_url
      "#{request.protocol}#{request.host_with_port}/logo_sea_education.jpg"
    end
  end
end
