/* Common styles */
.section-title {
  color: #2c3e50;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #3498db;
  font-weight: 600;
}

/* Introduction section */
.introduction-section {
  margin-bottom: 3rem;
}

.company-intro,
.nocn-intro {
  margin-bottom: 2rem;
}

.company-intro h2,
.nocn-intro h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.company-content p,
.nocn-content p {
  color: #34495e;
  line-height: 1.8;
  margin-bottom: 1rem;
}

/* Contact section */
.contact-section {
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.contact-section h2 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.contact-info {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.contact-item {
  text-align: center;
}

.contact-item i {
  font-size: 1.5rem;
  color: #3498db;
}

.contact-item span {
  color: #34495e;
  font-size: 0.9rem;
}

.contact-form {
  max-width: 500px;
  margin: 0 auto;
}

.contact-form h3 {
  color: #2c3e50;
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.form-description {
  color: #7f8c8d;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  color: #34495e;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  display: block;
}

.form-control {
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  padding: 0.75rem;
  width: 100%;
  transition: border-color 0.3s ease;
  font-size: 0.9rem;
}

.form-control:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-control.is-invalid {
  border-color: #e74c3c;
}

.form-control.is-invalid:focus {
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.invalid-feedback {
  color: #e74c3c;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: block;
}

.alert {
  padding: 0.75rem 1rem;
  margin-bottom: 1.25rem;
  border-radius: 4px;
  border: 1px solid transparent;
  font-size: 0.9rem;
}

.alert-success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.btn-primary {
  background-color: #3498db;
  border-color: #3498db;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  color: #fff;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-primary:hover {
  background-color: #2980b9;
  border-color: #2980b9;
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
}

/* NOCN Certificate section */
.nocn-certificate-section {
  margin-bottom: 3rem;
}

.benefits-list,
.audience-list {
  list-style: none;
  padding-left: 0;
}

.benefits-list li,
.audience-list li {
  color: #34495e;
  line-height: 1.8;
  margin-bottom: 1rem;
  padding-left: 1.5rem;
  position: relative;
}

.benefits-list li:before,
.audience-list li:before {
  content: "•";
  color: #3498db;
  position: absolute;
  left: 0;
}

.certificate-content {
  background-color: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
}

.certificate-content h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

/* Exam Info section */
.exam-info-section {
  margin-bottom: 3rem;
}

.exam-parts {
  display: grid;
  gap: 2rem;
}

.exam-part {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.exam-part h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.exam-details {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
}

.exam-details p {
  margin-bottom: 0.5rem;
}

.exam-details ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}

.exam-details li {
  color: #34495e;
  margin-bottom: 0.25rem;
}

.exam-description {
  color: #34495e;
  line-height: 1.8;
}

.exam-description p {
  margin-bottom: 1rem;
}

.exam-description ul {
  list-style: none;
  padding-left: 0;
}

.exam-description li {
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
}

.exam-description li:before {
  content: "•";
  color: #3498db;
  position: absolute;
  left: 0;
}

.word-count-table {
  margin-top: 2rem;
}

.table {
  width: 100%;
  margin-bottom: 0;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.table th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  padding: 1rem;
  border-bottom: 2px solid #e0e0e0;
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
  color: #34495e;
}

.table tr:last-child td {
  border-bottom: none;
}

/* FAQ section */
.faq-section {
  margin-bottom: 3rem;
}

.accordion-item {
  border: 1px solid #e0e0e0;
  margin-bottom: 1rem;
  border-radius: 4px;
  overflow: hidden;
}

.accordion-button {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  padding: 1rem;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.accordion-button:hover {
  background-color: #e9ecef;
}

.accordion-button:not(.collapsed) {
  background-color: #3498db;
  color: #fff;
}

.accordion-body {
  padding: 1.5rem;
  color: #34495e;
  line-height: 1.8;
}

/* Responsive styles */
@media (max-width: 768px) {
  .contact-info {
    flex-direction: column;
    gap: 1.5rem;
  }

  .exam-parts {
    grid-template-columns: 1fr;
  }

  .table {
    display: block;
    overflow-x: auto;
  }
} 

.contact-text {
  color: #34495e;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Contact section */
.contact-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.contact-section h2 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.contact-item i {
  font-size: 1.25rem;
  color: #3498db;
  width: 24px;
}

.contact-item span {
  color: #000;
  font-size: 0.95rem;
}

/* Rest of the CSS remains unchanged */ 