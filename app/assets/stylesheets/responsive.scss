@media screen and (max-width: 1024px) {
  .main {
    width: 100% !important;
  }
  .main2 {
    width: 100% !important;
  }
  #ja-khungchinh .footer2,
  #ja-khungchinh .header1,
  #ja-khungchinh .header2,
  #ja-khungchinh #ja-extra > div,
  #ja-khungchinh .dinhhuong1 {
    padding-left: 10px;
    padding-right: 10px;
  }
  select::-ms-expand {
    display: none;
  }
}

@media screen and (max-width: 990px) {
  .header {
    width: 100%;
    padding: 0 15px;
  }
  
  .left-header,
  .right-header {
    width: 100%;
    text-align: center;
  }

  .right-header {
    margin-top: 10px;
  }
}

/* Ipad */
@media screen and (max-width: 1024px) {
  /* Ở chế độ màn hình này, reset các thành phần bị quy định chiều ngang là px, ví dụ .main {width:950px;} */
  .main {
    width: 100% !important;
  }
  .main2 {
    width: 100% !important;
  }
  .nutflytocart i,
  .nutaddtocart i {
    display: none;
  }
  input.addtocart-button,
  input.flytocart-button {
    padding-left: 10px;
    padding-right: 10px;
  }
  #Mod366 {
    width: 100%;
  }
}
/* Điện thoại: mobile cũ :  @media screen and (max-width: 768px){ */
@media screen and (max-width: 767px) {
  .rsform .FormRegistration .rsform-block-anhtruoc,
  .rsform .FormRegistration .rsform-block-anhsau,
  .rsform .FormRegistration .rsform-block-anh46,
  .rsform .FormRegistration .rsform-block-anhtruoc .formControlLabel,
  .rsform .FormRegistration .rsform-block-anhsau .formControlLabel,
  .rsform .FormRegistration .rsform-block-anh46 .formControlLabel {
    text-align: Center;
  }
  #ja-cottrai {
    padding: 0%;
    width: 100%;
    float: none;
  }
  #ja-cotphai {
    padding: 0%;
    width: 100%;
    float: none;
  }
  #ja-cotgiua {
    width: 100%;
    float: none;
  }
  #ja-cottrai,
  #ja-cotphai {
    display: none;
  }
  .browse-view .blocksp {
    display: block;
    padding: 0%;
    width: 50%;
    margin: 0%;
  }
  .browse-view .row {
    display: inherit;
  }
  .browse-view .nutaddtocart,
  .browse-view .nutflytocart {
    margin: 3px 0px;
  }
  .browse-view .spacer {
    padding: 10px;
  }
  .browse-view .browseProductImage {
    width: auto;
    margin: auto;
  }
  .catItemImageBlock {
    margin: 0%;
    width: 40%;
    margin-bottom: 0px;
    margin-right: 10px;
  }
  .catItemThongTin {
    width: auto;
    float: none;
  }
  .catItemTitle {
    width: 100%;
    float: none;
    text-align: left;
  }
  .catItemReadMore {
    width: 100%;
    float: none;
  }
  .itemListView.scroller .catItemView .catItemImageBlock {
    margin-bottom: 10px;
  }
  .header-imageproduct {
    display: block;
    overflow: hidden;
    width: 100%;
    float: none;
    padding: 0%;
    border: 1px solid #ddd;
    background: white;
  }
  .header-descproduct {
    display: block;
    width: 100%;
    float: none;
    margin-top: 20px;
  }
  #ja-cotgiua img {
    max-width: 100%;
  }
  .bvcungmuc #k2ModuleRelated .k2ItemImageleft {
    width: 30%;
    float: left;
    margin-right: 5%;
  }
  .bvcungmuc #k2ModuleRelated h3 {
    width: 65%;
    float: right;
  }
  #ja-menungang,
  #Mod326 {
    display: none;
  }
  .mobile_menu {
    display: table;
    position: relative;
    width: 100%;
    table-layout: fixed;
  }
  .mobile_menu > div {
    display: table-cell;
    width: 100%;
  }
  .mobile_menu .tieude {
    padding: 10px;
    text-align: center;
    background: #36b1d5;
    color: white;
    font-weight: bold;
    font-size: 130%;
    cursor: pointer;
    text-transform: uppercase;
  }
  .mobile_menu .nentat {
    position: fixed;
    z-index: 999;
    background: rgba(0, 0, 0, 0.6);
    left: 0%;
    top: 0%;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  .mobile_menu .noidung {
    position: fixed;
    z-index: 1000;
    width: 70%;
    left: -70%;
    top: 0%;
    height: 100%;
    background: white;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .mobile_menu .noidung,
  .mobile_menu .nentat {
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
  }
  .mobile_menu .noidung > ul {
    padding: 10px;
  }
  .mobile_menu .noidung > ul > li {
    margin-bottom: 10px;
  }
  .mobile_menu .noidung > ul ul {
    margin-left: 20px;
    padding: 0;
  }
  .mobile_menu .noidung li {
    list-style-type: none;
  }
  .mobile_menu .noidung li a {
    padding: 5px;
    display: block;
    color: #005175;
  }
  .mobile_menu .noidung > ul > li > a {
    font-weight: bold;
  }
  .mobile_menu .noidung > ul > li > a:before {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: "\f35a";
    padding-right: 7px;
  }
  .mobile_menu .nentat {
    display: none;
  }
  .mobile_menu .noidung.mnmb {
    left: 0%;
  }
  .mobile_menu .nentat.mnmb {
    display: block;
  }
  .td3,
  .sd3 {
    font-size: 0% !important;
    width: 0px !important;
  }
  span.cart-images img {
    width: 100%;
    margin-right: 0px;
  }
  .mobile_menu i {
    padding-right: 10px;
  }
  .itemFullText td,
  .itemFullText tr,
  .itemFullText table,
  .product-thongtin-mota td,
  .product-thongtin-mota tr,
  .product-thongtin-mota table {
    display: block;
    width: 100% !important;
  }
  span.cart-images img {
    height: auto;
  }
  .td4 {
    width: 18%;
  }
  .td6 {
    width: 29%;
  }
  .cart-summary > tbody > tr:first-child td {
    font-size: 100%;
  }
  .td4 {
    width: 24%;
  }
  .tranggiohang .product-thongtin-salesPrice {
    font-size: 100%;
  }
  .tranggiohang .product-thongtin-billTotal {
    font-size: 110%;
  }
  button.details-button {
    font-size: 0px;
    padding: 7px 3px;
  }
  button.details-button:before {
    font-family: "Font Awesome 5 Free";
    font-weight: 400;
    content: "\f0c7";
    font-size: 14px;
    padding: 7px 5px;
  }
  .coupon {
    width: 70%;
  }
  .formdn,
  .order-view {
    float: none;
    width: 100%;
    clear: both;
    display: block;
    overflow: hidden;
  }
  .formdk {
    width: 100%;
    float: none;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
  }
  .order-view {
    margin-bottom: 20px;
  }
  .header1_3 .ja-workshome {
    float: none;
  }
  .header1_3 td {
    display: block;
    padding: 0px;
  }
  .header1 .main2 > div {
    display: block;
  }
  .footer2 .main2 > div {
    display: block;
    padding: 10px 0px;
  }
  .header2_2 .module_search {
    width: auto;
  }
  .popup_cart {
    width: 80%;
  }
  .popup_cart > div {
    padding: 0px 15px;
  }
  .n1 input {
    width: 24px;
  }
  .header1 .main2 > div {
    width: 100%;
    margin-top: 13px;
  }
  .header1 .header1_2 {
    margin-top: 0px !important;
  }
  .header1_1 tr td {
    display: block;
    width: 100%;
    text-align: center;
    padding: 0 !important;
  }
  .header1_3 {
    display: none !important;
  }
  #ja-menungang {
    display: block;
  }
  #Mod366 {
    display: none;
  }
  #Mod370 {
    width: 55%;
  }
  #Mod370 img {
    width: 100%;
    height: auto;
    padding: 10px;
  }
  #Mod377 {
    width: 15%;
    margin-right: 5px !important;
  }
  #Mod387 {
    width: 30%;
    padding: 10px;
    box-sizing: border-box;
  }
  #Mod387 img {
    width: 100%;
    height: 20px;
  }
  .mobile_menu .mobile_1 {
    display: none;
  }
  .mobile_menu {
    background: #2490dc;
  }
  .mobile_menu .noidung li a {
    color: black;
    padding: 0;
    font-weight: 400 !important;
  }
  .mobile_menu .noidung > ul > li > a:before {
    display: none;
  }
  .mobile_menu li.parent > a {
    display: inline !important;
  }
  .mobile_menu li.parent > span {
    float: right;
    text-align: right;
    display: inline-block;
    width: 20%;
  }
  .mobile_menu .noidung > ul ul {
    display: none;
  }
  .mobile_menu .noidung > ul > li {
    border-bottom: 1px solid #ccc;
    padding-bottom: 8px;
  }
  .mobile_menu ul li ul li a {
    padding: 5px 0 !important;
  }
  .noidung {
    background: rgba(0, 85, 132, 0.85) !important;
  }
  .mobile_menu .noidung li a {
    color: white;
  }
  .mobile_menu li.parent > span {
    color: white;
  }
  .mobile_menu .image-title {
    display: inline-block;
  }

  .extra1a ul li {
    width: 100%;
  }
  .browse-view .blocksp {
    width: 49%;
    margin-right: 2% !important;
    margin-bottom: 2%;
  }
  .browse-view .blocksp:nth-child(2n) {
    margin-right: 0 !important;
  }
  .browse-view .spacer {
    margin-bottom: 0 !important;
    display: block;
    height: 500px;
  }
  .product-thongtin {
    width: 100% !important;
  }

  .product-anh {
    width: 100%;
  }

  .browse-view .browseProductImage {
    width: 100%;
  }
  .product-thongtin-ten a {
    font-size: 120% !important;
  }
  #tabContainer ul li {
    width: 100% !important;
    text-align: center;
    margin-bottom: 10px;
    border-bottom: 1px solid #ddd;
  }
  .itemFullText img {
    width: 100%;
    height: auto;
  }
  .header2_1 {
    width: 40%;
  }
  .header2_3 {
  }
  .header2_4 {
    width: 95px;
  }
  #Mod387,
  #Mod377,
  #Mod370 {
    width: auto;
    padding: 0;
  }
  .DiaDiemVaLichThi_2_1 {
    width: auto;
    padding-right: 20px;
  }
  .DiaDiemVaLichThi_2_1,
  .DiaDiemVaLichThi_2_2 {
    display: block;
  }
  .DiaDiemVaLichThi_2_2 td:last-child {
    width: 80px;
  }
  .rsform .FormRegistration .rsform-block {
    margin-right: 0 !important;
    margin-bottom: 20px;
  }
  
  .rsform .FormRegistration .rsform-block-diachichuyenphat,
  .rsform .FormRegistration .rsform-block-lydokhac,
  .rsform .FormRegistration .rsform-block-khokhankhalac {
    width: 100% !important;
    float: none !important;
    clear: both !important;
  }
  .mobile_menu li.parent > span.separator {
    display: inline;
    padding: 0;
    font-weight: 400 !important;
    float: none;
    text-align: left;
    width: auto;
  }
  .extra1c-table {
    width: 100%;
    display: block;
  }
  .extra1c_1,
  .extra1c_2 {
    display: block;
  }
  .extra1c_2 {
    padding-left: 0;
    padding-top: 30px;
    text-align: center;
    width: 100%;
  }
  .extra1c_2 img {
    display: block;
    max-width: 100% !important;
    margin: auto;
    width: auto !important;
    height: auto !important;
  }
}
