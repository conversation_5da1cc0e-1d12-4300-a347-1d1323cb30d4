.table-active {
  background-color: #f8f9fa;
  border-radius: 10px;
}

.table-active td {
  padding: 0.75rem;
  vertical-align: top;
}

.table-active tr:hover {
  background-color: #e9ecef;
}

.input-group {
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.input-group .form-control {
  height: 50px;
  border: 1px solid #e0e0e0;
  border-radius: 4px 0 0 4px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.input-group .form-control:focus {
  border-color: #1a237e;
  box-shadow: 0 0 0 0.2rem rgba(26,35,126,0.25);
}

.input-group .btn-primary {
  height: 50px;
  background-color: #1a237e;
  border-color: #1a237e;
  border-radius: 0 4px 4px 0;
  padding: 0.75rem 2rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.input-group .btn-primary:hover {
  background-color: #0d1642;
  border-color: #0d1642;
}

.input-group .btn-primary:active {
  transform: translateY(0);
  box-shadow: none;
}

#content_result {
  margin-top: 2rem;
}

.search-result {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  padding: 2rem;
}

.search-result h4 {
  color: #1a237e;
  font-size: 1.25rem;
  font-weight: 600;
  position: relative;
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
}

.search-result h4:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: #1a237e;
}

.search-result .table {
  margin-bottom: 0;
  border: 1px solid #e0e0e0;
}

.search-result .table th,
.search-result .table td {
  padding: 1rem;
  vertical-align: middle;
  border: 1px solid #e0e0e0;
}

.search-result .table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  width: 30%;
}

.search-result .table td {
  color: #333;
}

.search-result p {
  color: #666;
  font-size: 1rem;
  margin-bottom: 1.5rem;
}

.background-white {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 2rem 0;
}

.container {
  max-width: 1140px;
  margin: 0 auto;
  padding: 0 15px;
}

.nav-right {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.nav-right p {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .search-result {
    padding: 1rem;
  }
  
  .search-result .table th,
  .search-result .table td {
    padding: 0.75rem;
  }
  
  .row {
    margin: 0 -5px;
  }
  
  .col-md-6 {
    padding: 0 5px;
    margin-bottom: 1rem;
  }
  
  .input-group .btn-primary {
    padding: 0.75rem 1rem;
  }
}