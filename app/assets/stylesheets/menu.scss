.mobile_menu {
  display: none;
}
.main2 {
  width: 1140px;
  margin: auto;
}
.menungang .menu a {
  padding: 10px 15px;
  display: block;
  font-size: 110%;
}
.level0 {
  display: table;
  width: 100%;
  padding: 0;
  margin: 0;
}
#ja-menungang .menu > li > a {
  text-align: center;
  color: white;
  font-weight: bold;
  text-transform: uppercase;
}
#ja-menungang .menu > li {
  display: table-cell;
}
.level0 ul {
  background: white;
}
#ja-menungang ul ul a:hover {
  background: #008065;
  color: white;
}
.menungang .menu ul {
  display: none;
  position: absolute;
  z-index: 10;
  width: 200px;
}
#ja-menungang .menu li:hover > ul {
  display: block;
  list-style: none;
  padding: 0;
}
.menungang .menu li {
  position: relative;
}
.menungang .menu ul ul {
  top: 0px;
  margin-left: 198px;
}
#ja-menungang .menu > .deeper > a:after,
#ja-header .menu > .deeper > a:after {
  content: "\f107";
  font-family: "Font Awesome 6 Free";
  font-weight: bold;
  padding-left: 5px;
}
#ja-noidunggiua .menu a {
  padding: 7px;
  display: block;
}
#ja-noidunggiua .menu ul {
  position: relative;
}
#ja-noidunggiua .level0 ul {
  top: 0px;
  box-shadow: 1px 1px 3px 1px gray;
}
#ja-noidunggiua .menu ul {
  display: none;
}
#ja-noidunggiua .menu > li {
  position: relative;
}
#ja-noidunggiua .menu li:hover > ul {
  display: block;
  position: absolute;
  z-index: 10;
  background: white;
  width: 200px;
  transform: translate(-100%, 0%);
  -webkit-transform: translate(-100%, 0%);
}
#ja-menungang .menu > li.active > a {
  background: #2490dc;
  color: white !important;
  padding: 10px !important;
  border-radius: 7px;
}
#Mod366 {
  width: 100%;
}
#Mod366 .menu > li > a {
  margin: 10px;
}
.menungang .main2 {
  display: flex;
  align-items: center;
}
#Mod366 .menu > li > a {
  padding: 10px !important;
  color: black !important;
}
#Mod366 .menu > li > a:hover {
  background: #2490dc;
  color: white !important;
  padding: 10px !important;
  border-radius: 7px;
}
#Mod366 .menu li ul li a {
  border-top: 1px solid rgb(162, 159, 159);
  padding: 10px;
  color: black;
  font-weight: bold;
}
#Mod366 .menu li ul li ul {
  width: 250px;
}
#Mod366 .menu li ul {
  border-left: 1px solid rgb(215, 212, 212);
  border-right: 1px solid rgb(215, 212, 212);
}
#Mod366 .menu li ul li a:hover {
  background: #2490dc;
  color: white;
}
.menungang {
  background: #2490dc;
}
#Mod366 .menu > li > a {
  color: white !important;
  margin: 0;
}
#Mod366 .menu > li > a:hover {
  color: #eee !important;
}

#Mod366 .menu > li > span {
  display: block;
  font-size: 110%;
  text-align: center;
  color: white;
  font-weight: bold;
  text-transform: uppercase;
  padding: 10px !important;
  margin: 0;
}

/* Bootstrap Dropdown Customization for Menu */
#menu .dropdown-menu {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  min-width: 200px;
}

#menu .dropdown-item {
  color: #333;
  padding: 8px 16px;
  text-decoration: none;
  display: block;
  transition: background-color 0.3s ease;
}

#menu .dropdown-item:hover {
  background-color: #2490dc;
  color: white;
}

#menu .dropdown-toggle::after {
  margin-left: 0.5em;
}

#menu .nav-item.dropdown:hover .dropdown-menu {
  display: block;
}

#menu .dropdown:hover .dropdown-toggle {
  color: #eee !important;
}
