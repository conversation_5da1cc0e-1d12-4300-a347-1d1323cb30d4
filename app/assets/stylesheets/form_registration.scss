#ja-noidunggiua {
  overflow: hidden;
  margin-top: 30px;
}

.workshome-title h2 {
  background: url("line_sp02.png") no-repeat center bottom;
  padding: 5px 15px 5px 15px;
  line-height: 1.8;
  padding-bottom: 20px;
  text-align: center;
  font-weight: bold;
  text-transform: uppercase;
  color: #0e65a2;
  display: block;
  font-size: 200%;
  font-family: "Roboto Condensed", Helvetica, Arial, sans-serif;
  margin: 0px;
}

#ja-cotgiua {
  background: transparent;
  min-height: 400px;
  float: left;
}

#ja-cotgiua .ja-workshome {
  margin-bottom: 15px;
}

#ja-cotgiua .workshome-content {
  background: white;
  padding: 10px;
  overflow: hidden;
}

// RS form
.rsform {
  input {
    outline: none !important;
    font-family: Arial, Helvetica, sans-serif;
  }
}

.formRed {
  color: red;
  font-weight: bold;
}

.formError {
  color: #cf4d4d;
  font-weight: bold;
  font-size: 10px;
}

.formNoError {
  display: none;
}

.formField {
  display: block;
  overflow: auto;
}

.formClr {
  clear: both;
  display: block;
}

fieldset.formFieldset {
  margin-bottom: 10px;
}

fieldset.formFieldset legend {
  padding: 0 2px;
  font-weight: bold;
  font-size: 16px;
}

fieldset.formFieldset ol.formContainer {
  margin: 0;
  padding: 0;
}

fieldset.formFieldset ol.formContainer li {
  background-image: none;
  list-style: none;
  padding: 5px;
  margin: 0;
  clear: both;
}

strong.formRequired {
  font-weight: bold;
  font-style: normal;
  margin-left: 3px;
}

div.formCaption {
  display: block;
  float: left;
  width: 25%;
  height: 12px;
}

div.formCaption {
  display: block;
}

div.formBody {
  display: block;
  float: left;
}

div.formDescription {
  margin-left: 3px;
  padding-left: 3px;
  font-size: 11px;
  font-weight: normal;
}

div.calheader {
  text-align: center !important;
}

.rsformProgress {
  text-align: center;
  font-size: 14px;
}

.rsformProgressContainer {
  width: 100%;
  height: 4px;
  border: solid 1px #c7c7c7;
  overflow: hidden;
  margin: 0 0 10px !important;
  background: #fff;
}

.rsformProgressBar {
  height: 4px;
  background: green;
  margin: 0px !important;
}

.rsformVerticalClear {
  overflow: hidden;
  display: block;
}

/* responsive - desktop/default version */
.rsform .rsformProgress p {
  display: none;
}

.rsform .rsformProgressContainer {
  height: 18px;
  margin-bottom: 18px;
  overflow: hidden;
  background-color: #f7f7f7;
  background-image: -moz-linear-gradient(top, #f5f5f5, #f9f9f9);
  background-image: -ms-linear-gradient(top, #f5f5f5, #f9f9f9);
  background-image: -webkit-gradient(
                  linear,
                  0 0,
                  0 100%,
                  from(#f5f5f5),
                  to(#f9f9f9)
  );
  background-image: -webkit-linear-gradient(top, #f5f5f5, #f9f9f9);
  background-image: -o-linear-gradient(top, #f5f5f5, #f9f9f9);
  background-image: linear-gradient(top, #f5f5f5, #f9f9f9);
  background-repeat: repeat-x;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#f5f5f5', endColorstr='#f9f9f9', GradientType=0);
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.rsform .rsformProgressContainer .rsformProgressBar {
  width: 0;
  height: 0;
  font-size: 12px;
  color: #ffffff;
  text-align: center;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #0e90d2;
  background-image: -moz-linear-gradient(top, #149bdf, #0480be);
  background-image: -webkit-gradient(
                  linear,
                  0 0,
                  0 100%,
                  from(#149bdf),
                  to(#0480be)
  );
  background-image: -webkit-linear-gradient(top, #149bdf, #0480be);
  background-image: -o-linear-gradient(top, #149bdf, #0480be);
  background-image: linear-gradient(top, #149bdf, #0480be);
  background-image: -ms-linear-gradient(top, #149bdf, #0480be);
  background-repeat: repeat-x;
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#149bdf', endColorstr='#0480be', GradientType=0);
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: width 0.6s ease;
  -moz-transition: width 0.6s ease;
  -ms-transition: width 0.6s ease;
  -o-transition: width 0.6s ease;
  transition: width 0.6s ease;
}

.rsform .formHorizontal input,
.rsform .formHorizontal textarea,
.rsform .formHorizontal select,
.rsform .formHorizontal .formValidation,
.rsform .formHorizontal .uneditable-input,
.rsform .formHorizontal .input-prepend,
.rsform .formHorizontal .input-append {
  display: block;
  margin-bottom: 0;
}

.rsform .formHorizontal .hide {
  display: none;
}

.rsform .formHorizontal .rsform-block {
  *zoom: 1;
}

.rsform .formHorizontal .rsform-block:before,
.rsform .formHorizontal .rsform-block:after {
  clear: both;
}

.rsform .formHorizontal .formControlLabel {
  float: left;
}

.rsform .formHorizontal .formControls {
  *display: inline-block;
  *padding-left: 20px;
  *margin-left: 0;
}

.rsform .formHorizontal .formControls:first-child {
  *padding-left: 160px;
}

.rsform .formHorizontal p.formDescription {
  margin-bottom: 0;
}

.rsform .formHorizontal .form-actions {
  padding-left: 160px;
}

.rsform fieldset {
  padding: 0;
  margin: 0;
  border: 0;
}

.rsform select,
.rsform textarea,
.rsform input[type="text"],
.rsform input[type="password"],
.rsform input[type="date"] {
  display: inline-block;
  padding: 4px;
}

.rsform input,
.rsform textarea {
  width: 210px;
}

.rsform textarea {
  height: auto;
}

.rsform input[type="radio"],
.rsform input[type="checkbox"] {
  margin: 3px 0;
  *margin-top: 0;
  /* IE7 */
  line-height: normal;
  cursor: pointer;
}

.rsform input[type="submit"],
.rsform input[type="reset"],
.rsform input[type="button"],
.rsform input[type="radio"],
.rsform input[type="checkbox"] {
  width: auto;
}

.rsform select {
  width: 220px;
  border: 1px solid #bbb;
}

.rsform select[multiple],
.rsform select[size] {
  height: auto;
}

.rsform select:focus,
.rsform input[type="file"]:focus,
.rsform input[type="radio"]:focus,
.rsform input[type="checkbox"]:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

.rsform input[type="radio"],
.rsform input[type="checkbox"] {
  /*min-height: 18px;*/
  padding-left: 18px;
  float: left;
  margin-right: 5px;
}

.rsform .formBody {
  float: none;
}

.rsform label {
  height: 18px;
  margin: 3px 5px 3px 0;
  float: left;
  padding: 0px;
}

.rsform input[type="button"],
.rsform input[type="submit"],
.rsform input[type="reset"] {
  border-color: #ccc;
}

.rsform input[type="button"],
.rsform input[type="submit"],
.rsform input[type="reset"] {
  display: inline-block;
  *display: inline;
  padding: 4px 10px 4px;
  margin-bottom: 0;
  *margin-left: 0.3em;
  *line-height: 20px;
  color: #333333;
  text-align: center;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
  vertical-align: middle;
  cursor: pointer;
  *background-color: #e6e6e6;
  border: 1px solid #cccccc;
  *border: 0;
  *zoom: 1;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2),
  0 1px 2px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2),
  0 1px 2px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2),
  0 1px 2px rgba(0, 0, 0, 0.05);
}

.rsform input[type="button"]:first-child,
.rsform input[type="reset"]:first-child,
.rsform input[type="submit"]:first-child {
  *margin-left: 0;
}

.rsform input[type="button"]:focus,
.rsform input[type="reset"]:focus,
.rsform input[type="submit"]:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

.rsform input[type="submit"] {
  background-color: #0074cc;
  *background-color: #0055cc;
  background-image: -ms-linear-gradient(top, #0088cc, #0055cc);
  background-image: -webkit-gradient(
                  linear,
                  0 0,
                  0 100%,
                  from(#0088cc),
                  to(#0055cc)
  );
  background-image: -webkit-linear-gradient(top, #0088cc, #0055cc);
  background-image: -o-linear-gradient(top, #0088cc, #0055cc);
  background-image: -moz-linear-gradient(top, #0088cc, #0055cc);
  background-image: linear-gradient(top, #0088cc, #0055cc);
  background-repeat: repeat-x;
  border-color: #0055cc #0055cc #003580;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#0088cc', endColorstr='#0055cc', GradientType=0);
  filter: progid:dximagetransform.microsoft.gradient(enabled=false);
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.rsform input[type="submit"]:hover,
.rsform input[type="submit"]:active,
.rsform input[type="submit"].active,
.rsform input[type="submit"].disabled,
.rsform input[type="submit"][disabled] {
  background-color: #0055cc;
  *background-color: #004ab3;
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.rsform input[type="submit"]:active,
.rsform input[type="submit"].active {
  background-color: #004099 \9
;
}

.rsform input[type="reset"] {
  background-color: #da4f49;
  *background-color: #bd362f;
  background-image: -ms-linear-gradient(top, #ee5f5b, #bd362f);
  background-image: -webkit-gradient(
                  linear,
                  0 0,
                  0 100%,
                  from(#ee5f5b),
                  to(#bd362f)
  );
  background-image: -webkit-linear-gradient(top, #ee5f5b, #bd362f);
  background-image: -o-linear-gradient(top, #ee5f5b, #bd362f);
  background-image: -moz-linear-gradient(top, #ee5f5b, #bd362f);
  background-image: linear-gradient(top, #ee5f5b, #bd362f);
  background-repeat: repeat-x;
  border-color: #bd362f #bd362f #802420;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ee5f5b', endColorstr='#bd362f', GradientType=0);
  filter: progid:dximagetransform.microsoft.gradient(enabled=false);
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.rsform input[type="reset"]:hover,
.rsform input[type="reset"]:active,
.rsform input[type="reset"].active,
.rsform input[type="reset"].disabled,
.rsform input[type="reset"][disabled] {
  background-color: #bd362f;
  *background-color: #a9302a;
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.rsform input[type="reset"]:active,
.rsform input[type="reset"].active {
  background-color: #942a25 \9
;
}

.rsform .formError {
  color: #b94a48;
  padding-left: 5px;
  vertical-align: middle;
  font-weight: normal;
  margin: 3px 5px 3px 0;
  height: 18px;
  display: block;
}

.rsform input.rsform-error:focus,
.rsform textarea.rsform-error:focus,
.rsform select.rsform-error:focus {
  border-color: #953b39;
  -webkit-box-shadow: 0 0 6px #d59392;
  -moz-box-shadow: 0 0 6px #d59392;
  box-shadow: 0 0 6px #d59392;
}

.rsform input.rsform-calendar-button {
  margin-bottom: 3px;
}

.rsform .rsform-block.rsform-block-noidung #NoiDung {
  border-radius: 0px;
}

.rsform .rsform-block.rsform-block-submit input {
  background: #3c3c3c;
  color: white;
}

.rsform {
  margin: 0px auto;
}

.rsform h2 {
  display: none;
}

.rsform .rsform-block {
  margin-bottom: 15px;
  position: relative;
}

.rsform input,
.rsform textarea {
  width: 100%;
}

.rsform-block-submit .formControls .formBody {
  text-align: center;
}

.formControlLabel {
  display: none;
}

.rsform input,
.rsform select,
.rsform textarea,
.rsform .inputbox {
  margin-top: 0px;
}

.rsform .rsform-block-guingay input {
  background: #3c3c3c;
  text-shadow: none;
  padding: 8px 15px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
}

.rsform select,
.rsform textarea,
.rsform input[type="text"],
.rsform input[type="password"],
.rsform input[type="date"] {
  display: inline-block;
  padding: 8px 10px;
  padding-left: 30px;
  border: 1px solid #ddd;
}

.rsform select {
  padding: 11px 10px;
}

.rsform select,
.rsform input[type="file"],
#userForm select {
  height: auto;
  width: 100%;
  margin-left: 0px;
}

.rsform input[type="button"] {
  padding: 7px 10px;
  margin-bottom: 0px;
}

.rsform input.rsform-error,
.rsform textarea.rsform-error,
.rsform select.rsform-error {
  border: 1px solid #b94a48;
}

.rsform-block:before {
  position: absolute;
  left: 10px;
  top: 11px;
  font: 100% "FontAwesome";
}

.rsform-block-hoten:before {
  content: "\f007";
}

.rsform-block-congty:before {
  content: "\f1ad";
}

.rsform-block-diachi:before {
  content: "\f041";
}

.rsform-block-dienthoai:before {
  content: "\f095";
}

.rsform-block-email:before {
  content: "\f0e0";
}

.rsform-block-noidung:before {
  content: "\f128";
}

.rsform p {
  display: none;
}

.formValidation {
  display: none !important;
}

.rsform .rsform-block-guingay input:hover {
  background: #d20707;
}

.rsform .rsform-block-guingay input {
  border: 0px;
}

.rsform-submit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 50px;

  input {
    background: #3c3c3c !important;
    padding: 7px 20px !important;
    line-height: 1.3;
    border-radius: 8px;
    font-size: 110%;
    font-weight: bold;
    color: white;
    text-transform: uppercase;

    &:hover {
      background: #d20707 !important;
    }
  }
}

.DropDownInput {
  position: relative;
}

.DropDownInput span {
  cursor: pointer;
}

.DropDownInput span:after {
  content: "\25BC";
  font-size: 12px;
  position: absolute;
  transform: translate(0%, -50%);
  top: 50%;
  right: 10px;
  z-index: 1;
}

.DropDownList ul {
  margin: 0;
  list-style: none;
  padding: 0;
  max-height: 300px;
  overflow-y: scroll;
  overflow-x: hidden;
}

.DropDownList input {
  padding: 0px 10px;
  line-height: 22px;
  width: 100%;
}

.DropDownList .DropDownListInput {
  padding: 10px;
  padding-right: 16px;
}

.DropDownList ul li {
  padding: 8px 10px;
  cursor: pointer;
}

.DropDownList ul li:hover {
  background: #017dc7;
  color: white;
}

.ListDown .DropDownList {
  display: block;
}

.ListDown .DropDownInput span:after {
  content: "\25B2";
}

.DropDown {
  position: relative;
}

.DropDownList {
  background: whitesmoke;
  display: none;
  position: absolute;
  width: calc(100% - 2px);
  margin-top: 10px;
  border: 1px solid #333;
  z-index: 2;
}

.DropDownList .DropDownListClose {
  padding-bottom: 10px;
}

.DropDownListClose {
  text-align: right;
}

.DropDownListClose span {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  background: #017dc7;
  color: white;
}

#DangKyThi .formControlLabel {
  display: block;
}

#DangKyThi .rsform-block:before {
  display: none;
}

#DangKyThi .rsform select,
#DangKyThi .rsform textarea,
#DangKyThi .rsform input[type="text"],
#DangKyThi .rsform input[type="password"],
#DangKyThi .rsform input[type="date"] {
  padding-left: 10px;
}

.formBody {
  clear: both;
}

.rsform-block-loaibaithi {
  float: left;
  width: 20%;
  margin-right: 2%;
}

.rsform-block-tinhthanhpho {
  width: 20%;
  float: left;
  margin-right: 2%;
}

.rsform-block-diadiemthi {
  float: left;
  width: 42%;
  margin-right: 2%;
}

.rsform-block-ngaythi {
  float: left;
  width: 24%;
}

.rsform-block-donvicongtac {
  clear: both;
  float: left;
  width: 42%;
  margin-right: 2%;
}

.rsform-block-truongdangcongtac,
.rsform-block-ctydangcongtac {
  float: right;
  width: 56%;
}

.rsform-block-ho {
  clear: both;
  float: left;
  width: 28%;
  margin-right: 2%;
}

.rsform-block-ten {
  float: left;
  width: 12%;
  margin-right: 2%;
}

.rsform-block-ngaysinh {
  float: left;
  width: 8%;
  margin-right: 2%;
}

.rsform-block-thangsinh {
  float: left;
  width: 8%;
  margin-right: 2%;
}

.rsform-block-namsinh {
  float: left;
  width: 12%;
  margin-right: 2%;
}

.rsform-block-gioitinh {
  float: left;
}

.rsform-block-capdodangki {
  float: left;
  width: 100%;
  display: flex;
  align-items: center;
}

.rsform-block-capdodangki .formControlLabel {
  width: auto;
  margin-right: 20px;
  margin-bottom: 0;
  flex-shrink: 0;
}

.rsform-block-capdodangki .formControls {
  flex: 1;
  margin-left: 0;
}

.rsform-block-sodienthoai {
  clear: both;
  float: left;
  width: 48%;
  margin-right: 2%;
}

.rsform-block-email {
  float: left;
  width: 50%;
}

.rsform-block-diachilienhe {
  float: left;
  width: 100%;
}

.rsform-block-giaytotuythan {
  clear: both;
  float: left;
  width: 20%;
  margin-right: 2%;
}

.rsform-block-sogiayto {
  float: left;
  width: 20%;
  margin-right: 2%;
}

.rsform-block-ngayhethan {
  float: left;
  width: 18%;
  margin-right: 2%;
}

.rsform-block-noicap {
  float: left;
  width: 36%;
}

.rsform-block-anhtruoc {
  clear: both;
  float: left;
  width: 16%;
  margin-right: 2%;
}

.rsform-block-anhsau {
  float: left;
  width: 16%;
  margin-right: 2%;
}

.rsform-block-anh46 {
  float: left;
  width: 16%;
  margin-right: 2%;
}

.rsform-block-lydochonthi {
  float: right;
  width: 46%;
}

.rsform-block-gapkhokhan {
  float: left;
  width: 100%;
}

.rsform-block-hotrokhokhan {
  float: left;
  width: 100%;
}

.rsform-block-phuongthucnhanchungchi {
  float: left;
  width: 100%;
}

.rsform-block-diachichuyenphat {
  float: left;
  width: 100%;
}

.rsform-block-lydokhac {
  float: left;
  width: 46%;
}

.rsform-block-khokhankhalac {
  float: left;
  width: 100%;
}

.rsform-block-xacnhan {
  clear: both;
  overflow: hidden;
}

#DangKyThi .rsform-block-guingay {
  clear: both;
}

#DangKyThi .formControlLabel {
  font-size: 110%;
  font-weight: bold;
  margin-bottom: 5px;
}

.rsform-block-truongdangcongtac .formControlLabel,
.rsform-block-ctydangcongtac .formControlLabel,
.rsform-block-thangsinh .formControlLabel,
.rsform-block-namsinh .formControlLabel,
.rsform-block-sogiayto .formControlLabel,
.rsform-block-noicap .formControlLabel,
.rsform-block-hotrokhokhan .formControlLabel,
.rsform-block-xacnhan .formControlLabel {
  visibility: hidden;
}

.rsform-block-ctydangcongtac {
  display: none;
}

select {
  border: 1px solid #ccc;
  background: url(../images/select.png) no-repeat right 6px center,
  linear-gradient(to top, #fff, #fff);
  background: url(../images/select.png) no-repeat right 6px center,
  -ms-linear-gradient(to top, #fff, #fff);
  background: url(../images/select.png) no-repeat right 6px center,
  -o-linear-gradient(to top, #fff, #fff);
}

.rsform select {
  padding: 8px 10px;
}

.rsform select,
.rsform textarea,
.rsform input[type="text"],
.rsform input[type="password"],
.rsform input[type="date"] {
  border: 1px solid #2490dc;
}

.rsform .rsform-block {
  margin-bottom: 30px;
}

.formBody label {
  font-size: 14px;
  margin: 0;
  margin: 5px 0px;
}

.rsform input[type="radio"],
.rsform input[type="checkbox"] {
  margin: 10px 0px;
}

.rsform input[type="radio"] + label,
.rsform input[type="checkbox"] + label {
  padding-left: 8px;
}

#DonViCongTac1 {
  margin-left: 50px;
}

#GioiTinh1 {
  margin-left: 20px;
}

.rsform-block-capdodangki input[type="radio"] {
  width: 18px;
  height: 18px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: 2px solid #2490dc;
  border-radius: 3px;
  background-color: white;
  background-image: none;
  cursor: pointer;
  position: relative;
  margin: 0;
  flex-shrink: 0;
  outline: none;
}

.rsform-block-capdodangki input[type="radio"]:focus {
  outline: none;
  box-shadow: none;
}

.rsform-block-capdodangki input[type="radio"]:before {
  display: none;
}

.rsform-block-capdodangki input[type="radio"]:checked {
  background-color: #2490dc;
  border-color: #2490dc;
}

.rsform-block-capdodangki input[type="radio"]:checked::after {
  content: "";
  position: absolute;
  top: 2px;
  left: 6px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.rsform-block-capdodangki label {
  margin-left: 8px;
  margin-right: 25px;
  font-weight: bold;
  cursor: pointer;
  line-height: 18px;
  display: flex;
  align-items: center;
}

#CapDoB1,
#CapDoB2,
#CapDoC1 {
  margin-left: 20px;
}

.rsform-block-ngayhethan .formBody {
  position: relative;
}

.rsform-block-ngayhethan input[type="button"] {
  position: Absolute;
  width: 100%;
  left: 0;
  padding: 0;
  font-size: 0;
  border: 0;
  line-height: 38px;
  outline: none !important;
  background: transparent;
}

.rsform-block-xacnhan .formBody label {
  width: calc(100% - 30px);
  display: block;
}

.formBody label {
  height: auto;
}

#DangKyThi .rsform-block-gapkhokhan .formControlLabel,
#DangKyThi .rsform-block-xacnhan .formControlLabel,
#DangKyThi .rsform-block-guingay .formControlLabel {
  display: none;
}

#DangKyThi .rsform-block-guingay {
  float: right;
  width: 56%;
}

#DangKyThi input[readonly] {
  cursor: pointer;
}

.rsform .rsform-block-guingay input {
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 8px;
  font-size: 110%;
}

.rsform .rsform-block.rsform-block-gapkhokhan,
.rsform .rsform-block.rsform-block-hotrokhokhan {
  margin-bottom: 10px;
}

#DangKyThi .rsform-error .formControlLabel {
  color: red;
}

#DangKyThi .rsform-error .formBody > input,
#DangKyThi .rsform-error .formBody > textarea,
#DangKyThi .rsform-error .formBody > .DropDown .DropDownInput input {
  border-color: red;
}

.rsform-block-hotrokhokhan {
  display: none;
}

.rsform-block-lydochonthi .formControls.mt-2 {
  display: none;
}

.rsform-block-phuongthucnhanchungchi .formControls.mt-2 {
  display: none;
}

.rsform-block-hotrokhokhan .formControls.mt-2 {
  display: none;
}

.subtitlee {
  font-weight: normal;
  font-style: italic;
  font-size: 85%;
  margin-left: 5px;
}

.formBody label a {
  color: #005cb9;
}

.rsform-block-xacnhan .formBody,
.rsform-block-gioitinh .formBody,
.rsform-block-capdodangki .formBody {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0;
}

// Responsive cho mobile - title riêng 1 dòng, options trên 1 dòng
@media (max-width: 768px) {
  .rsform-block-capdodangki {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .rsform-block-capdodangki .formControlLabel {
    width: 100%;
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .rsform-block-capdodangki .formControls {
    width: 100%;
  }
  
  .rsform-block-capdodangki .formBody {
    justify-content: space-between;
  }
  
  .rsform-block-capdodangki .formBody label {
    flex: 1;
    margin-right: 5px;
    font-size: 14px;
  }
  
  .rsform-block-capdodangki .formBody label:last-child {
    margin-right: 0;
  }
}

.rsform-block-xacnhan.rsform-error .formBody,
.rsform-block-gioitinh.rsform-error .formBody,
.rsform-block-capdodangki.rsform-error .formBody {
  border: 1px solid red;
  padding-left: 10px;
  padding-right: 10px;
}

.rsform-block-anhtruoc,
.rsform-block-anhsau,
.rsform-block-anh46 {
  text-align: center;
}

#DangKyThi .rsform-block-anhtruoc .formControlLabel,
#DangKyThi .rsform-block-anhsau .formControlLabel,
#DangKyThi .rsform-block-anh46 .formControlLabel {
  float: none;
}

#DangKyThi .rsform-block-anhtruoc input,
#DangKyThi .rsform-block-anhsau input,
#DangKyThi .rsform-block-anh46 input {
  display: none;
}

#DangKyThi .fileToUpload {
  cursor: pointer;
}

.fileToUpload img {
  width: 100%;
  height: 135px;
  object-fit: contain;
  display: block;
}

.fileToUpload i {
  line-height: 90px;
}

.rsform-block-gapkhokhan {
  overflow: hidden;
}

.DiaDiemVaLichThi {
  font-size: 120%;
}

.DiaDiemVaLichThi {
  border: 1px solid #ccc;
}

.DiaDiemVaLichThi_1 {
  display: block;
  overflow: hidden;
  background: #f3f3f3;
}

.DiaDiemVaLichThi_1 span {
  padding: 10px;
  float: left;
  cursor: pointer;
}

.DiaDiemVaLichThi_1 span.ON {
  background: #2490dc;
  color: white;
}

.DiaDiemVaLichThi_2 > div {
  display: none;
  width: 100%;
}

.DiaDiemVaLichThi_2 > div.ON {
  display: table;
}

.DiaDiemVaLichThi_2_1,
.DiaDiemVaLichThi_2_2 {
  display: Table-cell;
  vertical-align: top;
  padding: 20px;
}

.DiaDiemVaLichThi_2_1 {
  width: 380px;
  padding-right: 50px;
}

.DiaDiemVaLichThi_2_2 table {
  width: 100%;
}

.DiaDiemVaLichThi_2_2 td {
  padding: 10px;
  border-top: 1px solid #ccc;
}

.DiaDiemVaLichThi_2_1_4 a {
  display: inline-block;
  padding: 5px 15px;
  background: #ea0035;
  color: white;
  border-radius: 5px;
}

.DiaDiemVaLichThi_2_1 > div {
  margin-bottom: 15px;
}

.DiaDiemVaLichThi_2_1 > div:last-child {
  margin-bottom: 0;
}

.rsform-block-ngaythi {
  margin-right: 2%;
  width: 22%;
}

.rsform-block-cathi {
  float: left;
  width: 32%;
}

.rsform-block-anhtruoc .formControls,
.rsform-block-anhsau .formControls,
.rsform-block-anh46 .formControls {
  min-height: 100px;
}

.rsform-block-donvicongtac .formControls .formBody label {
  font-weight: bold;
  font-size: 110%;
}

.rsform-block-thongbaoluuy {
  text-align: center;
  display: none;
}

#Ho,
#Ten {
  text-transform: uppercase;
}

/* responsive - mobile versions */
@media screen and (max-width: 767px) {
  .rsform .formHorizontal input[type="checkbox"],
  .rsform .formHorizontal input[type="radio"] {
    border: 1px solid #ccc;
  }
  .rsform .formHorizontal .rsform-block {
    float: none;
    width: auto;
    padding-top: 0;
    text-align: left;
  }
  .rsform .formHorizontal .formControls {
    margin-left: 0;
  }

  .rsform input[type="text"],
  .rsform input[type="password"],
  .rsform textarea,
  .rsform select,
  .rsform input[type="date"] {
    width: 100% !important;
  }
  .rsform .formHorizontal .formControlLabel {
    float: none;
    width: auto;
    padding-top: 0;
    text-align: left;
  }
}

// Thankyou
.thankyou-section {
  font-size: 14px;
  line-height: 1.8;
  display: none;

  span {
    display: inline;
  }

  p {
    display: block;
  }
}

.thankyou {
  background: #fff;
}

.thankyou h3 {
  text-transform: uppercase;
  font-size: 15px;
  background: #0099DB;
  padding: 8px 10px;
  color: #fff;
  font-weight: 700;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.thankyou ul {
  padding-left: 20px;
}

.thankyou ul li {
  padding: 5px 0;
  text-transform: uppercase;
}
