// Header 1
.header1 p {
  margin: 0;
  line-height: 1.8;
}
.header1 img {
  display: block;
}
.header1 .main2 {
  display: table;
}
.header1 .main2 > div {
  display: table-cell;
  vertical-align: middle;
}
.header1_3 .ja-workshome {
  float: right;
}
.header1 td {
  padding-left: 20px;
}
.header1 td:first-child {
  padding-left: 0px;
}
.header1 {
  background: #2490dc;
  color: white;
  padding: 3px 0px;
  line-height: 1;
  font-size: 110%;
}
#Mod377 {
  width: auto;
  text-align: right;
}
.module_search > * {
  margin: 5px 0px;
}
.mod_virtuemart_search {
  padding: 10px 10px;
  width: 100%;
  border: 1px solid #ccc;
  outline: none !important;
  &:hover {
    background-color: #fef8cc;
  }
}
#Mod377 .workshome-content {
  position: relative;
}
#Mod377 .workshome-content:hover > p {
  color: black;
}
#Mod377 .workshome-content > p {
  color: white;
  background: #2490dc;
  padding: 5px 6px;
  display: inline-block;
  border-radius: 3px;
  cursor: pointer;
}
#Mod377 .workshome-content > form {
  position: absolute;
  margin-top: 2px;
  width: 200px;
  z-index: 27;
  display: none;
  right: 0;
}
#Mod377 form {
  position: relative;
}
#Mod377 .module_search {
  position: relative;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 13px;
}
#Mod377 .buttontim {
  position: absolute;
  top: 0;
  right: 0;
  line-height: 1;
  padding: 10px 15px;
  display: inline-block;
  font-size: 15px;
  color: #333;
  font-weight: bold;
  cursor: pointer;
}
#Mod377 {
  margin-right: 15px;
}

#Mod377 .workshome-content > p {
  padding: 0;
  padding: 7px;
}

.sp-vmsearch-categories {
  display: none;
}

.linkmangxahoi i {
  background: white;
  color: #333;
  width: 24px;
  height: 24px;
  float: left;
  border-radius: 50%;
  margin-left: 5px;
  position: relative;
}
.linkmangxahoi i:before {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  left: 50%;
  top: 50%;
  position: absolute;
}
// Header 2
.header2 {
  padding: 10px 0px;
  p {
    -webkit-margin-before: 0em;
    -webkit-margin-after: 0em;
  }
}
.header2 .main2 {
  display: table;
}
.header2 .main2 > div {
  display: table-cell;
  vertical-align: middle;
}
.header2_4 .ja-workshome {
  float: right;
}
.header2_2 .module_search > * {
  margin: 0px;
  float: left;
}
.header2_2 .module_search {
  width: 400px;
}
.header2_2 .mod_virtuemart_search {
  width: 55%;
}
.header2_2 .module_search select {
  width: 35%;
}
.header2_2 .buttontim {
  padding-left: 0px;
  padding-right: 0px;
  width: 10%;
  text-align: center;
}
.header2_2 .module_search > * {
  margin: 0px;
  float: left;
}
.header2_2 .module_search {
  width: 400px;
}
.header2_2 .mod_virtuemart_search {
  width: 55%;
}
.header2_2 .module_search select {
  width: 35%;
}
.header2_2 .buttontim {
  padding-left: 0px;
  padding-right: 0px;
  width: 10%;
  text-align: center;
}
.header2 {
  display: block;
}
.header2_4 li {
  float: left;
}
.header2_4 li a {
  display: block;
}
.header2_4 {
  width: 120px;
}
#Mod370 {
  width: 30%;
}

#Mod370 img {
  padding: 10px 0;
  height: auto;
}

// SEA Education Logo
.sea-education-logo {
  display: flex;
  align-items: center;
  
  .logo-image {
    width: 160px;
    height: auto;
    max-width: 160px;
  }
}

// Header adjustments for better layout
#header {
  min-height: 140px;
  
  .header {
    min-height: 140px;
    display: flex;
    align-items: center;
  }
  
  .row {
    width: 100%;
    margin: 0;
    align-items: center;
  }
}

.left-header {
  display: flex;
  align-items: center;
}

.right-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  text-align: right;
  
  span {
    margin-left: 8px;
  }
}
