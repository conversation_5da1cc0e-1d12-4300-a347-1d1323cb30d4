/* Schedule Section Styles - Exact Copy */
.schedule-content {
  /* City Tabs */
  .city-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 0;
    margin-bottom: 1rem;
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;

    .city-tab {
      background: #e9ecef;
      border: 1px solid #dee2e6;
      color: #495057;
      padding: 0.5rem 1rem;
      margin-right: 0.25rem;
      margin-bottom: 0.25rem;
      cursor: pointer;
      font-size: 0.9rem;
      border-radius: 3px;

      &:hover {
        background: #dee2e6;
      }

      &.active {
        background: #6c757d;
        color: white;
        border-color: #6c757d;
      }
    }
  }

  /* City Schedule Content */
  .city-schedule {
    display: none;
    border: 1px solid #dee2e6;
    background: white;

    &.active {
      display: block;
    }
  }

  /* Schedule Info */
  .schedule-info {
    display: flex;
    border-bottom: 1px solid #dee2e6;

    .company-info {
      flex: 1;
      padding: 1rem;
      border-right: 1px solid #dee2e6;

      .company-name {
        font-size: 0.9rem;
        color: #333;
        margin-bottom: 0.5rem;
        line-height: 1.4;
      }

      .location-pin {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 1rem;
      }
    }

    .exam-info {
      flex: 2;
      display: flex;

      .exam-dates-section, .exam-type-section {
        flex: 1;
        padding: 1rem;

        .section-title {
          font-weight: bold;
          color: #333;
          margin-bottom: 0.5rem;
          text-align: center;
        }

        .date-group {
          font-size: 0.9rem;
          color: #333;
          margin-bottom: 0.3rem;
        }

        .exam-type {
          font-size: 0.9rem;
          color: #333;
        }
      }

      .exam-type-section {
        border-left: 1px solid #dee2e6;
      }
    }
  }

  /* Registration Info */
  .registration-info {
    padding: 1rem;
    background: #f8f9fa;

    .deadline {
      font-size: 0.9rem;
      color: #333;
      margin-bottom: 0.5rem;

      .highlight {
        color: #dc3545;
        font-weight: bold;
      }
    }

    .fee {
      font-size: 0.9rem;
      color: #333;
      margin-bottom: 1rem;
    }

    .register-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 3px;
      font-size: 0.9rem;
      cursor: pointer;

      &:hover {
        background: #c82333;
      }
    }
  }

  /* Responsive */
  @media (max-width: 768px) {
    .city-tabs {
      .city-tab {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
      }
    }

    .schedule-info {
      flex-direction: column;

      .company-info {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
      }

      .exam-info {
        flex-direction: column;

        .exam-type-section {
          border-left: none;
          border-top: 1px solid #dee2e6;
        }
      }
    }

    .registration-info {
      .register-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
      }
    }
  }

  /* Empty State */
  .empty-state {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
    border: 1px solid #dee2e6;
    background: #f8f9fa;
  }
}
