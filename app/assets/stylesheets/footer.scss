.footer2 {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  padding-top: 20px;
  padding-bottom: 40px;
  background: #3c3c3c;
  color: white;
}
.footer2 a {
  color: white;
  text-decoration: none;
}
.footer2 .main2 {
  display: table;
}
.footer2 .main2 > div {
  display: table-cell;
}
.footer2 p {
  text-wrap: wrap;
  margin-bottom: 2px;
}
.footer2_2 {
  padding-right: 30px;
  padding-left: 30px;
}
.footer2 .workshome-title h2 {
  text-transform: uppercase;
  font-size: 130%;
  padding-bottom: 3px;
  margin-bottom: 10px;
  border-bottom: 1px solid #a0a0a0;
  display: inline-block;
}
.footer2_2 {
  width: 340px;
}
.footer2 {
  background: #2f3031;
}
.footer2 img {
  width: 60px;
  height: 60px;
  border-radius: 20px;
}

.footer2 .menu ul {
  background: transparent;
}
