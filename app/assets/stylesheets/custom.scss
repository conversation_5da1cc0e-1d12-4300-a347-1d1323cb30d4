.background-white {
  background-color: #fff;
}

@media screen and (max-width: 991px) {
  #header {
    display: inline-table;
    font-size: 13px;
    width: 100%;
  }
}

#header {
  background-color: #fff !important;
}

#menu {
  background-color: #0099DB !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  
  .navbar {
    padding: 0.5rem 0;
  }

  .navbar-nav {
    margin: 0;
    padding: 0;
  }

  .nav-item {
    margin: 0 0.5rem;
    
    .nav-link {
      padding: 0.5rem 0;
      color: #333;
      font-weight: 500;
      transition: color 0.3s ease;
      
      &:hover {
        color: #007bff;
      }
      
      &.active {
        color: #007bff;
      }
    }
  }

  @media (max-width: 991.98px) {
    .navbar-collapse {
      background: #fff;
      border-radius: 0.25rem;
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      margin-top: 0.5rem;
    }

    .navbar-nav {
      padding: 0.5rem 0;
    }

    .nav-item {
      margin: 0;
      padding: 0.5rem 0;
      
      .nav-link {
        padding: 0.5rem 1rem;
      }
    }

    .navbar-toggler {
      padding: 0.5rem;
      border: 1px solid #0099da;
      border-radius: 0.25rem;
      top: 0;

      &:focus {
        box-shadow: none;
        outline: none;
      }
    }
  }
}

// Custom Hamburger to X Animation
.navbar-toggler {
  .hamburger-lines {
    display: block;
    width: 22px;
    height: 16px;
    position: relative;
    
    .line {
      display: block;
      height: 2px;
      width: 100%;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 2px;
      position: absolute;
      left: 0;
      transition: all 0.3s ease;
      transform-origin: center;
    }
    
    .line1 {
      top: 0;
    }
    
    .line2 {
      top: 7px;
    }
    
    .line3 {
      top: 14px;
    }
  }
  
  // When menu is open (not collapsed)
  &:not(.collapsed) {
    .hamburger-lines {
      .line1 {
        transform: rotate(45deg);
        top: 7px;
      }
      
      .line2 {
        opacity: 0;
        transform: scale(0);
      }
      
      .line3 {
        transform: rotate(-45deg);
        top: 7px;
      }
    }
  }
  
  // Hover effect
  &:hover {
    .hamburger-lines .line {
      background: rgba(255, 255, 255, 1);
    }
  }
}

// Mobile navbar improvements
#menu {
  .navbar {
    padding: 0.5rem 0;
    
    @media (max-width: 991.98px) {
      .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #0099da;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 0 0 8px 8px;
        z-index: 1000;
        margin-top: 0;
        max-height: calc(100vh - 80px);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        
        .navbar-nav {
          .nav-item {
            margin: 0;
            
            .nav-link {
              padding: 0.75rem 1.5rem;
              color: #fff !important;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);
              transition: all 0.3s ease;
              
              &:hover, &:focus {
                color: #fff !important;
              }
              
              &.main-color {
                color: #fff !important;
              }
            }
            
            &.dropdown {
              .dropdown-menu {
                background: #0099da;
                border: none;
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
                margin: 0;
                border-radius: 0;
                
                .dropdown-item {
                  color: rgba(255, 255, 255, 0.8);
                  padding: 0.5rem 2rem;
                  font-size: 0.9rem;
                  
                  &:hover, &:focus {
                    background-color: #0099da;
                    color: #fff;
                  }
                }
              }
            }
          }
        }
      }
      
      .navbar-toggler {
        padding: 5px;
        border: 1px solid #0099da;
        border-radius: 4px;
        background: transparent;
        
        &:focus {
          box-shadow: none;
          border-color: #0099da;
        }
        
        // Override default Bootstrap toggler icon
        .navbar-toggler-icon {
          background-image: none;
          width: auto;
          height: auto;
        }
      }
      
      .navbar-brand {
        color: #fff !important;
        font-size: 1.1rem;
        
        i {
          color: #fff !important;
        }
      }
    }
    
    // Desktop styles
    @media (min-width: 992px) {
      .navbar-nav {
        .nav-item {
          .nav-link {
            &.main-color {
              color: #fff !important;
            }
          }
          
          // &.dropdown {
          //   .dropdown-menu {
          //     .dropdown-item {
          //       &:hover, &:focus {
          //         background-color: #f8f9fa;
          //       }
          //     }
          //   }
          // }
        }
      }
    }
  }
}

// Ensure proper z-index for mobile menu
@media (max-width: 991.98px) {
  .navbar-collapse.show {
    display: block !important;
  }
}

// Search form styles
#submit-form {
  .input-group {
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border-radius: 4px;
    overflow: hidden;
    
    .form-control {
      border: 1px solid #ced4da;
      padding: 0.75rem 1rem;
      font-size: 0.95rem;
      height: calc(1.5em + 0.75rem + 2px);
      
      &:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
      }
    }
    
    .input-group-append {
      .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        height: calc(1.5em + 0.75rem + 2px);
        line-height: 1;
        background-color: #0056b3;
        border-color: #0056b3;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        
        &:active {
          transform: translateY(0);
          box-shadow: none;
        }
      }
    }
  }
}

.certificate-images {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: flex-start;
  
  a {
    flex: 1;
    min-width: 280px;
    display: block;
    text-decoration: none;
    cursor: pointer;
    
    img {
      width: 100%;
      height: auto;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        opacity: 0.9;
      }
    }
    
    &:hover {
      text-decoration: none;
    }
  }
  
  // Responsive adjustments
  @media (max-width: 991.98px) {
    gap: 15px;
    margin-top: 20px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    
    a {
      flex: 1;
      min-width: 45%;
      max-width: 48%;
    }
  }
  
  @media (max-width: 576px) {
    gap: 10px;
    max-width: 100%;
    
    a {
      min-width: 45%;
      max-width: 48%;
    }
  }
  
  @media (min-width: 992px) {
    a {
      flex: 1;
      min-width: 48%;
      max-width: 48%;
    }
  }
}

// Certificate Introduction Page Styles
.certificate-intro-container {
  background: #ffffff;
  min-height: 100vh;
  padding: 2rem 0;
  
  .page-title {
    color: #333;
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }
  
  .page-subtitle {
    color: #666;
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }
  
  .certificate-main-content {
    margin-top: 2rem;
  }
  
  .certificate-container {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    
    .section-title {
      color: #333;
      font-size: 1.8rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      text-align: center;
    }
    
    .instruction-text {
      color: #666;
      text-align: center;
      margin-bottom: 1.5rem;
      font-style: italic;
    }
  }
  
  .certificate-image-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
    max-width: 650px;
    margin: 0 auto;
    border-radius: 8px;
    overflow: visible;
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    
    .certificate-sample-image {
      width: 100%;
      height: auto;
      display: block;
    }
    
    .red-border-area {
      position: absolute;
      cursor: pointer;
      border: 3px solid #ff0000;
      transition: all 0.3s ease;
      background: transparent;
      
      &:hover {
        border-color: #cc0000;
        box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
        transform: scale(1.02);
      }
      
      &.active {
        border-color: #990000;
        box-shadow: 0 0 15px rgba(255, 0, 0, 0.7);
        background: rgba(255, 0, 0, 0.1);
      }
    }
    
    // Specific positioning for each area based on the certificate image
    .student-name-area {
      top: 31.5%;
      left: 20%;
      width: 60%;
      height: 6%;
    }
    
    .qualification-area {
      top: 48%;
      left: 8%;
      width: 84%;
      height: 8%;
    }
    
    .nocn-logo-area {
      top: 4%;
      right: 6%;
      width: 15%;
      height: 13%;
    }
    
    .signature-area {
      bottom: 3%;
      left: 4%;
      width: 21%;
      height: 12%;
    }
    
    .anti-fraud-area {
      bottom: 16%;
      left: 10%;
      width: 13%;
      height: 9%;
    }
    
    .qr-code-area {
      bottom: 18%;
      right: 7%;
      width: 43%;
      height: 8%;
    }
    
    .ofqual-logo-area {
      bottom: 3%;
      right: 6%;
      width: 19%;
      height: 9%;
    }

    .qualifications-wales-area {
      bottom: 2%;
      left: 58%;
      width: 15%;
      height: 11%;
    }

    .cea-area {
      bottom: 3%;
      left: 28%;
      width: 15%;
      height: 9%;
    }
  }
  
  .info-panel {
    margin-right: 2rem;
    background: #e8f4fd;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    position: sticky;
    top: 2rem;
    min-height: 400px;
    
    .info-content {
      animation: fadeIn 0.3s ease-in-out;
      
      h3 {
        color: #2c5aa0;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        border-bottom: 2px solid #2c5aa0;
        padding-bottom: 0.5rem;
      }
      
      p {
        color: #333;
        line-height: 1.6;
        margin-bottom: 1rem;
        text-align: justify;
      }
      
      ul {
        margin: 0;
        padding-left: 1.5rem;
        
        li {
          color: #555;
          margin-bottom: 0.5rem;
          line-height: 1.5;
          
          strong {
            color: #2c5aa0;
          }
        }
      }
    }
  }
  
  @media (max-width: 991.98px) {
    .page-title {
      font-size: 2rem;
    }
    
    .certificate-container,
    .info-panel {
      margin-bottom: 1.5rem;
    }
    
    .info-panel {
      position: static;
    }
  }
  
  @media (max-width: 576px) {
    padding: 1rem 0;
    
    .page-title {
      font-size: 1.6rem;
    }
    
    .certificate-container,
    .info-panel {
      padding: 1.5rem;
    }
    
    .clickable-area {
      border-width: 1px;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Ensure video-home section is always visible
.video-home {
  display: block !important;
  visibility: visible !important;
  
  // Override the #event img{display:none} rule
  img {
    display: block !important;
  }
  
  @media (max-width: 991.98px) {
    margin-top: 30px !important;
    text-align: center !important;
    width: 100% !important;
  }
}

// Force display certificate images - override global rules
#event .video-home img,
#event .certificate-images img {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}
