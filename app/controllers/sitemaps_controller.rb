class SitemapsController < ApplicationController
  def index
    respond_to do |format|
      format.xml { render layout: false }
    end
  end

  private

  def sitemap_urls
    urls = []

    # Static pages
    urls << {
      loc: root_url,
      lastmod: Date.current,
      changefreq: 'daily',
      priority: 1.0
    }

    urls << {
      loc: about_url,
      lastmod: Date.current,
      changefreq: 'monthly',
      priority: 0.8
    }

    urls << {
      loc: certificate_introduction_url,
      lastmod: Date.current,
      changefreq: 'monthly',
      priority: 0.8
    }

    urls << {
      loc: regulations_url,
      lastmod: Date.current,
      changefreq: 'monthly',
      priority: 0.8
    }

    urls << {
      loc: search_results_url,
      lastmod: Date.current,
      changefreq: 'weekly',
      priority: 0.7
    }

    urls << {
      loc: exam_registration_forms_url,
      lastmod: Date.current,
      changefreq: 'daily',
      priority: 0.9
    }

    urls << {
      loc: contact_url,
      lastmod: Date.current,
      changefreq: 'monthly',
      priority: 0.6
    }

    urls
  end

  helper_method :sitemap_urls
end
