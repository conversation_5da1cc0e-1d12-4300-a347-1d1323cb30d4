# frozen_string_literal: true

module Admin
  class ExamSessionsController < ApplicationController
    def index
      @exam_centers = Master::ExamCenter.all
    end

    def update
      exam_center = Master::ExamCenter.find(params[:exam_center_id])
      exam_day = exam_center.exam_days.includes(:exam_sessions).find(params[:exam_day_id])
      exam_session_data = exam_sessions_params[:exam_sessions].split(/\r?\n/).map(&:strip).reject(&:empty?)
      existed_exam_sessions = exam_day.exam_sessions.pluck(:time_range)
      new_exam_sessions = exam_session_data - existed_exam_sessions
      exam_sessions_need_to_delete = existed_exam_sessions - exam_session_data

      ActiveRecord::Base.transaction do
        new_exam_sessions.each { |session| exam_day.exam_sessions.create(time_range: session) }
        exam_day.exam_sessions.where(time_range: exam_sessions_need_to_delete).destroy_all
      end

      redirect_to admin_exam_sessions_path(exam_center_id: params[:exam_center_id], exam_day_id: params[:exam_day_id]),
                  notice: I18n.t('admin.exam_sessions.update.success')
    rescue StandardError => e
      redirect_to admin_exam_sessions_path(exam_center_id: params[:exam_center_id], exam_day_id: params[:exam_day_id]),
                  alert: I18n.t('admin.exam_sessions.update.error', message: e.message)
    end

    def load_by_day
      exam_center = Master::ExamCenter.find(params[:exam_center_id])
      exam_day = exam_center.exam_days.includes(:exam_sessions).find(params[:exam_day_id])
      exam_sessions = exam_day.exam_sessions.pluck(:time_range)
      render json: exam_sessions.as_json
    end

    private

    def exam_sessions_params
      params.permit(:exam_sessions, :exam_day_id, :exam_center_id)
    end
  end
end
