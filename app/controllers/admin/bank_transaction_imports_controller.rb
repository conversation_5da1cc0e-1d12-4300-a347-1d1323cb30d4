module Admin
  class BankTransactionImportsController < ApplicationController
    def index
      @imports = BankTransactionImport.recent.page(params[:page]).per(20)
      @latest_import = BankTransactionImport.latest.first
    end

    def show
      @import = BankTransactionImport.find(params[:id])
    end

    def new
      # Form upload file
    end

    def create
      uploaded_file = params[:transaction_file]

      if uploaded_file.blank?
        render json: { status: 'error', message: 'Vui lòng chọn file để upload' }
        return
      end

      # Lưu file tạm thời
      temp_file_path = Rails.root.join('tmp', 'uploads', uploaded_file.original_filename)
      FileUtils.mkdir_p(File.dirname(temp_file_path))

      File.binwrite(temp_file_path, uploaded_file.read)

      # Xử lý import
      service = BankTransactionImportService.new(temp_file_path, uploaded_file.original_filename)
      result = service.process

      # Xóa file tạm
      FileUtils.rm_f(temp_file_path)

      if result[:success]
        render json: {
          status: 'success',
          message: result[:summary],
          import_id: result[:import_record].id
        }
      else
        render json: {
          status: 'error',
          message: result[:error]
        }
      end
    end

    def destroy
      @import = BankTransactionImport.find(params[:id])

      if @import.destroy
        render json: { status: 'success', message: 'Đã xóa bản ghi import' }
      else
        render json: { status: 'error', message: 'Không thể xóa bản ghi import' }
      end
    end
  end
end
