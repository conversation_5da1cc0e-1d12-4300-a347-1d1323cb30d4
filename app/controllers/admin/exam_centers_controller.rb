# frozen_string_literal: true

module Admin
  class ExamCentersController < ApplicationController
    def index
      @exam_centers = Master::ExamCenter.all
    end

    def create
      @exam_center = Master::ExamCenter.new(exam_center_params)

      if @exam_center.save
        render json: { status: 'success', message: I18n.t('admin.exam_centers.create.success'),
                       exam_center: exam_center_json(@exam_center) }
      else
        render json: { status: 'error', message: @exam_center.errors.full_messages.join(', ') }
      end
    end

    def update
      @exam_center = Master::ExamCenter.find(params[:id])

      if @exam_center.update(exam_center_params)
        render json: { status: 'success', message: I18n.t('admin.exam_centers.update.success') }
      else
        render json: { status: 'error', message: @exam_center.errors.full_messages.join(', ') }
      end
    end

    def destroy
      @exam_center = Master::ExamCenter.find(params[:id])

      if @exam_center.destroy
        render json: { status: 'success', message: I18n.t('admin.exam_centers.destroy.success') }
      else
        render json: { status: 'error', message: I18n.t('admin.exam_centers.destroy.error') }
      end
    end

    def update_positions
      params[:positions].each_with_index do |id, index|
        # rubocop:disable Rails/SkipsModelValidations
        # Sử dụng update_all để cập nhật position hàng loạt với hiệu năng tốt
        # Validation không cần thiết cho việc cập nhật position đơn giản
        Master::ExamCenter.where(id: id).update_all(position: index + 1)
        # rubocop:enable Rails/SkipsModelValidations
      end

      render json: { status: 'success', message: I18n.t('admin.exam_centers.update_positions.success') }
    end

    def search
      exam_centers = Master::ExamCenter.ransack(address_cont: params[:q])
      render json: exam_centers.result.as_json
    end

    private

    def exam_center_params
      params.require(:exam_center).permit(:address, :is_visible, :position, :exam_fee)
    end

    def exam_center_json(exam_center)
      {
        id: exam_center.id,
        address: exam_center.address,
        is_visible: exam_center.is_visible,
        position: exam_center.position,
        exam_fee: exam_center.exam_fee
      }
    end
  end
end
