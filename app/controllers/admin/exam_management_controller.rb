# frozen_string_literal: true

module Admin
  class ExamManagementController < ApplicationController
    def index
      @exam_centers = Master::ExamCenter.includes(exam_days: :exam_sessions).order(:position, :id)
    end

    def update_center
      exam_center = Master::ExamCenter.find(params[:id])
      if exam_center.update(exam_center_params)
        render json: { success: true, message: 'Cậ<PERSON> nhật trung tâm thi thành công!' }
      else
        render json: { success: false, errors: exam_center.errors.full_messages }
      end
    end

    def create_center
      exam_center = Master::ExamCenter.new(exam_center_params)
      if exam_center.save
        render json: {
          success: true,
          message: 'Thêm trung tâm thi thành công!',
          center: exam_center.as_json(include: { exam_days: { include: :exam_sessions } })
        }
      else
        render json: { success: false, errors: exam_center.errors.full_messages }
      end
    end

    def destroy_center
      exam_center = Master::ExamCenter.find(params[:id])
      if exam_center.destroy
        render json: { success: true, message: '<PERSON><PERSON><PERSON> trung tâm thi thành công!' }
      else
        render json: { success: false, message: 'Không thể xóa trung tâm thi này!' }
      end
    end

    def create_day
      exam_center = Master::ExamCenter.find(params[:exam_center_id])
      exam_day = exam_center.exam_days.build(date: params[:date])

      if exam_day.save
        render json: {
          success: true,
          message: 'Thêm ngày thi thành công!',
          day: exam_day.as_json(include: :exam_sessions)
        }
      else
        render json: { success: false, errors: exam_day.errors.full_messages }
      end
    end

    def destroy_day
      exam_day = Master::ExamDay.find(params[:id])
      if exam_day.destroy
        render json: { success: true, message: 'Xóa ngày thi thành công!' }
      else
        render json: { success: false, message: 'Không thể xóa ngày thi này!' }
      end
    end

    def create_session
      exam_day = Master::ExamDay.find(params[:exam_day_id])
      exam_session = exam_day.exam_sessions.build(time_range: params[:time_range])

      if exam_session.save
        render json: {
          success: true,
          message: 'Thêm ca thi thành công!',
          session: exam_session
        }
      else
        render json: { success: false, errors: exam_session.errors.full_messages }
      end
    end

    def destroy_session
      exam_session = Master::ExamSession.find(params[:id])
      if exam_session.destroy
        render json: { success: true, message: 'Xóa ca thi thành công!' }
      else
        render json: { success: false, message: 'Không thể xóa ca thi này!' }
      end
    end

    private

    def exam_center_params
      params.permit(:address, :position, :exam_center_id)
    end
  end
end
