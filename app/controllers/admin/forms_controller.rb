# frozen_string_literal: true

module Admin
  class FormsController < ApplicationController
    def index
      @cities_name = Master::City.all.map(&:name).join("\n")
      @colleges_name = Master::College.all.map(&:name).join("\n")
      @identity_document_types = Master::IdentityDocumentType.all.map(&:name).join("\n")
      @support_choices = Master::SupportChoice.all.map(&:name).join("\n")
      @registration_reasions = Master::RegistrationReason.all.map(&:description).join("\n")
      @certificate_delivery_methods = Master::CertificateDeliveryMethod.all.map(&:name).join("\n")

      @exam_centers = Master::ExamCenter.all
      @exam_center_addresses = Master::ExamCenter.all.map(&:address).join("\n")
    end

    def update_cities
      Admin::MasterDataService.update_cities(update_cities_params[:cities])
      render_success_response(I18n.t('admin.cities.update.success'))
    rescue StandardError
      render_error_response(I18n.t('admin.cities.update.error', message: ''))
    end

    def update_colleges
      Admin::MasterDataService.update_colleges(update_colleges_params[:colleges])
      render_success_response(I18n.t('admin.colleges.update.success'))
    end

    def update_identity_document_types
      Admin::MasterDataService.update_identity_document_types(
        update_identity_document_types_params[:identity_document_types]
      )
      render_success_response(I18n.t('admin.identity_document_types.update.success'))
    end

    def update_support_choices
      Admin::MasterDataService.update_support_choices(update_support_choices_params[:support_choices])
      render_success_response(I18n.t('admin.support_choices.update.success'))
    end

    def update_registration_reasons
      Admin::MasterDataService.update_registration_reasons(
        update_registration_reasons_params[:registration_reasons]
      )
      render_success_response(I18n.t('admin.registration_reasons.update.success'))
    end

    def update_certificate_delivery_methods
      Admin::MasterDataService.update_certificate_delivery_methods(
        update_certificate_delivery_methods_params[:certificate_delivery_methods]
      )
      render_success_response(I18n.t('admin.certificate_delivery_methods.update.success'))
    rescue StandardError
      render_error_response(I18n.t('admin.certificate_delivery_methods.update.error', message: ''))
    end

    def exam_centers_data
      exam_centers = Master::ExamCenter.ransack(address_cont: params[:q])
      render json: exam_centers.result.as_json
    end

    def update_exam_centers
      Admin::MasterDataService.update_exam_centers(exam_centers_params[:exam_center_addresses])
      render_success_response(I18n.t('admin.exam_centers.update.success'))
    end

    def load_exam_days
      exam_center = Master::ExamCenter.find(params[:exam_center_id])
      exam_days = exam_center.exam_days.pluck(:id, :date).map { |d| { id: d[0], date: d[1].strftime('%d/%m/%Y') } }
      render json: exam_days.as_json
    end

    def load_exam_sessions
      exam_center = Master::ExamCenter.find(params[:exam_center_id])
      exam_day = exam_center.exam_days.includes(:exam_sessions).find(params[:exam_day_id])
      exam_sessions = exam_day.exam_sessions.pluck(:time_range)
      render json: exam_sessions.as_json
    end

    def update_exam_days
      exam_center = Master::ExamCenter.find(params[:exam_center_id])
      exam_day_data = exam_days_params[:exam_days].split(/\r?\n/).map(&:strip).reject(&:empty?)
      existed_exam_day = exam_center.exam_days.pluck(:date).map { |d| d.strftime('%d/%m/%Y') }
      new_exam_days = exam_day_data - existed_exam_day
      exam_day_need_to_delete = existed_exam_day - exam_day_data

      ActiveRecord::Base.transaction do
        new_exam_days.each { |exam_day_name| exam_center.exam_days.create(date: exam_day_name) }
        exam_center.exam_days.where(date: exam_day_need_to_delete).destroy_all
      end

      render_success_response(I18n.t('admin.exam_days.update.success'))
    rescue StandardError
      render_error_response(I18n.t('admin.exam_days.update.error', message: ''))
    end

    def update_exam_sessions
      exam_center = Master::ExamCenter.find(params[:exam_center_id])
      exam_day = exam_center.exam_days.includes(:exam_sessions).find(params[:exam_day_id])
      exam_session_data = exam_sessions_params[:exam_sessions].split(/\r?\n/).map(&:strip).reject(&:empty?)
      existed_exam_sessions = exam_day.exam_sessions.pluck(:time_range)
      new_exam_sessions = exam_session_data - existed_exam_sessions
      exam_sessions_need_to_delete = existed_exam_sessions - exam_session_data

      ActiveRecord::Base.transaction do
        new_exam_sessions.each { |session| exam_day.exam_sessions.create(time_range: session) }
        exam_day.exam_sessions.where(time_range: exam_sessions_need_to_delete).destroy_all
      end

      render_success_response(I18n.t('admin.exam_sessions.update.success'))
    end

    def update_exam_center_visibility
      selected_center_ids = exam_center_visibility_params[:exam_center_ids] || []

      ActiveRecord::Base.transaction do
        # rubocop:disable Rails/SkipsModelValidations
        # Đặt tất cả về false trước - sử dụng update_all để cập nhật hàng loạt với hiệu năng tốt
        Master::ExamCenter.update_all(is_visible: false)

        # Đặt những cái được chọn về true
        Master::ExamCenter.where(id: selected_center_ids).update_all(is_visible: true) if selected_center_ids.any?
        # rubocop:enable Rails/SkipsModelValidations
      end

      visible_count = selected_center_ids.length
      total_count = Master::ExamCenter.count

      success_message = I18n.t('admin.exam_centers.update_visibility.success',
                               visible_count: visible_count, total_count: total_count)
      render_success_response(success_message)
    rescue StandardError => e
      render_error_response(I18n.t('admin.exam_centers.update_visibility.error', message: e.message))
    end

    def export
      cities = Master::City.all
      respond_to do |format|
        format.xls do
          file_name = "Cities-#{Time.now.strftime('%Y%m%d%H%M%S')}.xls"
          send_data(cities.to_a.to_xls, type: "text/xls; charset=utf-8; header=present", filename: file_name)
        end
      end
    end

    private

    def render_success_response(message)
      respond_to do |format|
        format.html { redirect_to admin_root_path, notice: I18n.t('admin.common.success') }
        format.json { render json: { status: 'success', message: message } }
      end
    end

    def render_error_response(message)
      respond_to do |format|
        format.html { redirect_to admin_root_path, alert: I18n.t('admin.common.error') }
        format.json { render json: { status: 'error', message: message }, status: :unprocessable_entity }
      end
    end

    def update_cities_params
      params.permit(:cities)
    end

    def update_colleges_params
      params.permit(:colleges)
    end

    def update_identity_document_types_params
      params.permit(:identity_document_types)
    end

    def update_support_choices_params
      params.permit(:support_choices)
    end

    def update_registration_reasons_params
      params.permit(:registration_reasons)
    end

    def update_certificate_delivery_methods_params
      params.permit(:certificate_delivery_methods)
    end

    def exam_centers_params
      params.permit(:exam_center_addresses)
    end

    def exam_days_params
      params.permit(:exam_days, :exam_center_id)
    end

    def exam_sessions_params
      params.permit(:exam_sessions, :exam_day_id, :exam_center_id)
    end

    def exam_center_visibility_params
      params.permit(exam_center_ids: [])
    end
  end
end
