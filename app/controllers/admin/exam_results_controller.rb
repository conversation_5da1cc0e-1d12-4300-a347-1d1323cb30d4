module Admin
  class ExamResultsController < ApplicationController
    def index
      @exam_results = ExamResult.all
      if params[:identification_number].present?
        @exam_results = @exam_results.where("identification_number = ?",
                                            params[:identification_number])
      end
      @exam_results = @exam_results.page(params[:page])

      respond_to do |format|
        format.html
        format.js
      end
    end

    def new
    end

    def import
      if params[:file].present?
        begin
          ExamResult.import(params[:file])
          redirect_to admin_exam_results_path, notice: I18n.t('exam_results.import.success')
        rescue StandardError => e
          redirect_to new_admin_exam_result_path, alert: I18n.t('exam_results.import.error', error: e.message)
        end
      else
        redirect_to new_admin_exam_result_path, alert: I18n.t('exam_results.import.no_file')
      end
    end
  end
end
