# frozen_string_literal: true

module Admin
  class CitiesController < ApplicationController
    def index
      @cities_name = Master::City.all.map(&:name).join("\n")
    end

    def update
      Admin::MasterDataService.update_cities(cities_params[:cities])
      redirect_to admin_cities_path, notice: I18n.t('admin.cities.update.success')
    rescue StandardError => e
      redirect_to admin_cities_path, alert: I18n.t('admin.cities.update.error', message: e.message)
    end

    private

    def cities_params
      params.permit(:cities)
    end
  end
end
