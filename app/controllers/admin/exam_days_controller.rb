# frozen_string_literal: true

module Admin
  class ExamDaysController < ApplicationController
    def index
      @exam_centers = Master::ExamCenter.all
    end

    def update
      exam_center = Master::ExamCenter.find(params[:exam_center_id])
      exam_day_data = exam_days_params[:exam_days].split(/\r?\n/).map(&:strip).reject(&:empty?)
      existed_exam_day = exam_center.exam_days.pluck(:date).map { |d| d.strftime('%d/%m/%Y') }
      new_exam_days = exam_day_data - existed_exam_day
      exam_day_need_to_delete = existed_exam_day - exam_day_data

      ActiveRecord::Base.transaction do
        new_exam_days.each { |exam_day_name| exam_center.exam_days.create(date: exam_day_name) }
        exam_center.exam_days.where(date: exam_day_need_to_delete).destroy_all
      end

      redirect_to admin_exam_days_path(exam_center_id: params[:exam_center_id]),
                  notice: I18n.t('admin.exam_days.update.success')
    rescue StandardError => e
      redirect_to admin_exam_days_path(exam_center_id: params[:exam_center_id]),
                  alert: I18n.t('admin.exam_days.update.error', message: e.message)
    end

    def load_by_center
      exam_center = Master::ExamCenter.find(params[:exam_center_id])
      exam_days = exam_center.exam_days.pluck(:id, :date).map { |d| { id: d[0], date: d[1].strftime('%d/%m/%Y') } }
      render json: exam_days.as_json
    end

    private

    def exam_days_params
      params.permit(:exam_days, :exam_center_id)
    end
  end
end
