# frozen_string_literal: true

module Admin
  class CollegesController < ApplicationController
    def index
      @colleges_name = Master::College.all.map(&:name).join("\n")
    end

    def update
      Admin::MasterDataService.update_colleges(colleges_params[:colleges])
      redirect_to admin_colleges_path, notice: I18n.t('admin.colleges.update.success')
    rescue StandardError => e
      redirect_to admin_colleges_path, alert: I18n.t('admin.colleges.update.error', message: e.message)
    end

    private

    def colleges_params
      params.permit(:colleges)
    end
  end
end
