module Admin
  class ContactsController < ApplicationController
    before_action :authenticate_user!
    before_action :set_contact, only: %i[show destroy]

    def index
      @contacts = Contact.order(created_at: :desc).page(params[:page])
    end

    def show; end

    def destroy
      @contact.destroy
      redirect_to admin_contacts_path, notice: t('.success')
    end

    private

    def set_contact
      @contact = Contact.find(params[:id])
    end
  end
end
