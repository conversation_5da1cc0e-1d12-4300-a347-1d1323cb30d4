# frozen_string_literal: true

module Admin
  class SupportChoicesController < ApplicationController
    def index
      @support_choices = Master::SupportChoice.all.map(&:name).join("\n")
    end

    def update
      Admin::MasterDataService.update_support_choices(support_choices_params[:support_choices])
      redirect_to admin_support_choices_path, notice: I18n.t('admin.support_choices.update.success')
    rescue StandardError => e
      redirect_to admin_support_choices_path, alert: I18n.t('admin.support_choices.update.error', message: e.message)
    end

    private

    def support_choices_params
      params.permit(:support_choices)
    end
  end
end
