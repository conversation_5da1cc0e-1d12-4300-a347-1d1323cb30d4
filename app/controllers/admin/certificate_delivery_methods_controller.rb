# frozen_string_literal: true

module Admin
  class CertificateDeliveryMethodsController < ApplicationController
    def index
      @certificate_delivery_methods = Master::CertificateDeliveryMethod.all.map(&:name).join("\n")
    end

    def update
      Admin::MasterDataService.update_certificate_delivery_methods(
        certificate_delivery_methods_params[:certificate_delivery_methods]
      )
      redirect_to admin_certificate_delivery_methods_path,
                  notice: I18n.t('admin.certificate_delivery_methods.update.success')
    rescue StandardError => e
      redirect_to admin_certificate_delivery_methods_path,
                  alert: I18n.t('admin.certificate_delivery_methods.update.error', message: e.message)
    end

    private

    def certificate_delivery_methods_params
      params.permit(:certificate_delivery_methods)
    end
  end
end
