# frozen_string_literal: true

module Admin
  class RegistrationReasonsController < ApplicationController
    def index
      @registration_reasons = Master::RegistrationReason.all.map(&:description).join("\n")
    end

    def update
      Admin::MasterDataService.update_registration_reasons(
        registration_reasons_params[:registration_reasons]
      )
      redirect_to admin_registration_reasons_path, notice: I18n.t('admin.registration_reasons.update.success')
    rescue StandardError => e
      error_message = I18n.t('admin.registration_reasons.update.error', message: e.message)
      redirect_to admin_registration_reasons_path, alert: error_message
    end

    private

    def registration_reasons_params
      params.permit(:registration_reasons)
    end
  end
end
