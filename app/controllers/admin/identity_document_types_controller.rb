# frozen_string_literal: true

module Admin
  class IdentityDocumentTypesController < ApplicationController
    def index
      @identity_document_types = Master::IdentityDocumentType.all.map(&:name).join("\n")
    end

    def update
      Admin::MasterDataService.update_identity_document_types(
        identity_document_types_params[:identity_document_types]
      )
      redirect_to admin_identity_document_types_path, notice: I18n.t('admin.identity_document_types.update.success')
    rescue StandardError => e
      error_message = I18n.t('admin.identity_document_types.update.error', message: e.message)
      redirect_to admin_identity_document_types_path, alert: error_message
    end

    private

    def identity_document_types_params
      params.permit(:identity_document_types)
    end
  end
end
