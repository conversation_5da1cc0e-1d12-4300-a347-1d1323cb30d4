class ExamRegistrationFormsController < ApplicationController
  include Admin::ExamRegistrationFormsHelper

  def index
    set_meta_tags_for_page(
      title: 'Đăng ký thi chứng chỉ NOCN - Form đăng ký online',
      description: 'Đăng ký thi chứng chỉ tiếng Anh NOCN online tại SEA Education. ' \
                   'Chọn trung tâm thi, lịch thi phù hợp. Thanh toán online, nhận chứng chỉ quốc tế được công nhận.',
      keywords: 'đăng ký thi NOCN online, form đăng ký thi tiếng anh, chứng chỉ NOCN, ' \
                'SEA Education, đăng ký thi trực tuyến'
    )

    load_master_data
    load_test_data if Rails.env.development?
  end

  def create
    create_params = exam_registration_form_params
    create_params[:gender] = create_params[:gender].to_i
    create_params[:work_location_type] = create_params[:work_location_type].to_i
    create_params[:need_specific_support] = create_params[:need_specific_support] == 'true'
    if create_params[:work_location_type] == 1
      create_params[:company_name] = ''
    else
      create_params[:college_name] = ''
    end
    @exam_registration_form = ExamRegistrationForm.new(create_params)

    if @exam_registration_form.save
      ActiveRecord::Base.transaction do
        if create_params[:id_front_image].present?
          @exam_registration_form.id_front_image.attach(create_params[:id_front_image])
        end
        if create_params[:id_back_image].present?
          @exam_registration_form.id_back_image.attach(create_params[:id_back_image])
        end
        if create_params[:profile_image].present?
          @exam_registration_form.profile_image.attach(create_params[:profile_image])
        end

        exam_center = Master::ExamCenter.visible.find_by(address: create_params[:exam_center_name])
        raise ActiveRecord::RecordNotFound, 'Exam center not found or not available' if exam_center.nil?

        # Set payment amount từ exam center
        @exam_registration_form.update!(payment_amount: exam_center.exam_fee)

        exam_day = exam_center.exam_days.find_by(date: create_params[:exam_day])
        raise ActiveRecord::RecordNotFound, 'Exam session not found' if exam_day.nil?

        exam_session = exam_day.exam_sessions.find_by(time_range: create_params[:exam_time_range])
        raise ActiveRecord::RecordNotFound, 'Exam session not found' if exam_session.nil?

        exam_session.slot -= 1
        raise ActiveRecord::RecordInvalid, exam_session.errors.full_messages.join(', ') unless exam_session.save

        payment_info = nil
        begin
          qr_service = QrPaymentService.new
          payment_info = qr_service.generate_payment_qr(@exam_registration_form)
          id_document_number = @exam_registration_form.id_document_number
        rescue StandardError => e
          Rails.logger.error "QR generation error: #{e.message}"
          Rails.logger.error "Backtrace: #{e.backtrace.join("\n")}"
        end

        # UserMailer.with(user: @exam_registration_form).registration_confirmation.deliver_later
        render json: {
          message: 'Exam registration form created successfully!',
          payment_info: payment_info,
          id_document_number: id_document_number
        }, status: :created
      rescue StandardError => e
        Rails.logger.error "Registration error: #{e.message}"
        Rails.logger.error "Backtrace: #{e.backtrace.join("\n")}"
        raise ActiveRecord::Rollback
      end
    else
      Rails.logger.error "Failed to save exam registration form: #{@exam_registration_form.errors.full_messages}"
      render json: { errors: @exam_registration_form.errors.full_messages }, status: :unprocessable_entity
    end
  end

  private

  def load_master_data
    @master_colleges = Master::College.all.map { |college| { value: college.name, label: college.name } }
    @master_exam_centers = load_exam_centers
    @master_exam_days = load_exam_days
    @master_exam_sessions = load_exam_sessions
    @master_id_document_types = load_id_document_types
    @master_registration_reasons = load_registration_reasons
    @master_support_choices = load_support_choices
    @master_certificate_delivery_methods = load_certificate_delivery_methods
  end

  def load_exam_centers
    Master::ExamCenter.visible.map do |exam_center|
      { value: exam_center.address, label: exam_center.address, id: exam_center.id }
    end
  end

  def load_exam_days
    Master::ExamDay.joins(:exam_center)
                   .where(exam_centers: { is_visible: true })
                   .each_with_object([]) do |exam_day, result|
      next unless exam_day.date >= 12.days.from_now.to_date

      result << {
        value: format_date(exam_day.date),
        label: format_date(exam_day.date),
        parent_id: exam_day.exam_center_id,
        id: exam_day.id
      }
    end
  end

  def load_exam_sessions
    Master::ExamSession.joins(exam_day: :exam_center)
                       .where(exam_centers: { is_visible: true })
                       .each_with_object([]) do |exam_session, result|
      next unless exam_session.slot.positive?

      result << {
        id: exam_session.id,
        value: exam_session.time_range,
        label: "#{exam_session.time_range}",
        parent_id: exam_session.exam_day_id
      }
    end
  end

  def load_id_document_types
    Master::IdentityDocumentType.all.map do |id_document_type|
      { value: id_document_type.name, label: id_document_type.name }
    end
  end

  def load_registration_reasons
    Master::RegistrationReason.all.map do |registration_reason|
      { value: registration_reason.description, label: registration_reason.description }
    end
  end

  def load_support_choices
    Master::SupportChoice.all.map do |support_choice|
      { value: support_choice.name, label: support_choice.name }
    end
  end

  def load_certificate_delivery_methods
    Master::CertificateDeliveryMethod.all.map do |method|
      { value: method.name, label: method.description }
    end
  end

  def load_test_data
    @test_data = {
      lastname: 'NGUYEN VAN',
      firstname: 'AN',
      birth_day: '15',
      birth_month: '06',
      birth_year: '1995',
      gender: '1',
      work_location_type: '1',
      college_name: @master_colleges.first&.dig(:value) || '',
      company_name: '',
      id_document_type: @master_id_document_types.first&.dig(:value) || '',
      id_document_number: '*********',
      id_expiry_date: '2030-12-31',
      id_issue_place: 'Hà Nội',
      email: '<EMAIL>',
      phone_number: '0*********',
      detailed_contact_address: '123 Đường ABC, Quận XYZ, Hà Nội',
      exam_center_name: @master_exam_centers.first&.dig(:value) || '',
      exam_level: 'B1',
      registration_reason: @master_registration_reasons.first&.dig(:value) || '',
      need_specific_support: false,
      certificate_delivery_method: @master_certificate_delivery_methods.first&.dig(:value) || ''
    }
  end

  def exam_registration_form_params
    params.require(:exam_registration_form).permit(
      :email,
      :phone_number,
      :firstname,
      :lastname,
      :birthday,
      :gender,
      :work_location_type,
      :company_name,
      :id_document_number,
      :id_expiry_date,
      :id_issue_place,
      :need_specific_support,
      :support_choice,
      :other_support_choice,
      :detailed_contact_address,
      :college_name,
      :exam_time_range,
      :id_document_type,
      :registration_reason,
      :other_registration_reason,
      :exam_center_name,
      :exam_day,
      :certificate_delivery_method,
      :delivery_address,
      :other_difficulty,
      :id_front_image,
      :id_back_image,
      :profile_image,
      :exam_level
    )
  end
end
