class ContactsController < ApplicationController
  # <PERSON><PERSON><PERSON> vệ chống CSRF
  protect_from_forgery with: :exception

  def index
    set_meta_tags_for_page(
      title: '<PERSON><PERSON>n hệ SEA Education - Thông tin liên lạc và hỗ trợ',
      description: '<PERSON><PERSON><PERSON> hệ với SEA Education để được tư vấn về chứng chỉ NOCN. ' \
                   'Địa chỉ: 26 <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Hà Nội. Hotline: +84 886681666. Email: <EMAIL>',
      keywords: 'liên hệ SEA Education, tư vấn NOCN, địa chỉ SEA Education, hotline, email'
    )
  end

  def create
    @contact = Contact.new(contact_params)

    if @contact.save
      # Log thông tin liên hệ (không log thông tin nhạy cảm)
      Rails.logger.info "Nhận được liên hệ mới từ: #{@contact.email} - Tên: #{@contact.name}"

      # Gửi email thông báo (c<PERSON> thể bật lại sau)
      begin
        # ContactMailer.contact_email(@contact).deliver_now
        # ContactMailer.admin_notification(@contact).deliver_now
      rescue StandardError => e
        Rails.logger.error "Lỗi gửi email: #{e.message}"
      end

      respond_to do |format|
        format.json do
          success_message = "Cảm ơn #{@contact.name}! Chúng tôi đã nhận được tin nhắn của bạn và " \
                            "sẽ phản hồi trong vòng 24 giờ qua email #{@contact.email}."
          render json: {
            success: true,
            message: success_message
          }
        end
        format.html do
          redirect_to contact_path, notice: "Tin nhắn của bạn đã được gửi thành công!"
        end
      end
    else
      Rails.logger.warn "Lỗi tạo contact: #{@contact.errors.full_messages.join(', ')}"

      respond_to do |format|
        format.json do
          render json: {
            success: false,
            message: "Có lỗi xảy ra khi gửi tin nhắn. Vui lòng kiểm tra lại thông tin.",
            errors: format_errors(@contact.errors.messages)
          }
        end
        format.html do
          redirect_to contact_path, alert: "Có lỗi xảy ra. Vui lòng thử lại."
        end
      end
    end
  rescue StandardError => e
    Rails.logger.error "Lỗi hệ thống khi tạo contact: #{e.class} - #{e.message}"
    Rails.logger.error e.backtrace.join("\n") if Rails.env.development?

    respond_to do |format|
      format.json do
        render json: {
          success: false,
          message: "Có lỗi hệ thống xảy ra. Vui lòng thử lại sau hoặc liên hệ trực tiếp qua hotline +84 886681666."
        }
      end
      format.html do
        redirect_to contact_path, alert: "Có lỗi hệ thống. Vui lòng thử lại."
      end
    end
  end

  private

  def contact_params
    params.require(:contact).permit(:name, :email, :message)
  end

  # Format lỗi để hiển thị thân thiện hơn
  def format_errors(errors_hash)
    formatted_errors = {}

    errors_hash.each do |field, messages|
      formatted_errors[field] = messages.map { |message| format_field_error(field, message) }
    end

    formatted_errors
  end

  def format_field_error(field, message)
    case field.to_s
    when 'name'
      format_name_error(message)
    when 'email'
      format_email_error(message)
    when 'message'
      format_message_error(message)
    else
      message
    end
  end

  def format_name_error(message)
    case message
    when /blank/i
      "Vui lòng nhập họ và tên"
    when /too short/i
      "Họ tên quá ngắn (tối thiểu 2 ký tự)"
    when /too long/i
      "Họ tên quá dài (tối đa 100 ký tự)"
    else
      message
    end
  end

  def format_email_error(message)
    case message
    when /blank/i
      "Vui lòng nhập địa chỉ email"
    when /invalid/i
      "Địa chỉ email không đúng định dạng"
    when /too long/i
      "Email quá dài (tối đa 255 ký tự)"
    else
      message
    end
  end

  def format_message_error(message)
    case message
    when /blank/i
      "Vui lòng nhập nội dung tin nhắn"
    when /too short/i
      "Nội dung tin nhắn quá ngắn (tối thiểu 10 ký tự)"
    when /too long/i
      "Nội dung tin nhắn quá dài (tối đa 1000 ký tự)"
    else
      message
    end
  end
end
