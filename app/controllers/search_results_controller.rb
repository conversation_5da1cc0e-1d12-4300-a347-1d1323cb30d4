class SearchResultsController < ApplicationController

  def index
    set_breadcrumb('Tra cứu kết quả', search_results_path)

    set_meta_tags_for_page(
      title: I18n.t('search_results.meta.title', default: 'Tra cứu kết quả thi NOCN - Kiểm tra điểm thi online'),
      description: I18n.t('search_results.meta.description',
                          default: 'Tra cứu kết quả thi chứng chỉ NOCN online nhanh chóng và chính xác. ' \
                                   'Nhập CMND/CCCD/Passport hoặc ID chứng chỉ để xem điểm thi và thông tin chứng chỉ.'),
      keywords: 'tra cứu kết quả thi NOCN, kiểm tra điểm thi, kết quả thi tiếng anh, tra cứu chứng chỉ NOCN online'
    )

    @result = nil
    if params[:keyword].present?
      @result = ExamResult.find_by(identification_number: params[:keyword])

      # Cập nhật meta tags nế<PERSON> tì<PERSON> th<PERSON>y kết quả
      if @result
        set_meta_tags_for_page(
          title: I18n.t('search_results.result.title',
                        name: @result.student_name,
                        default: "Kết quả thi của #{@result.student_name} - NOCN"),
          description: I18n.t('search_results.result.description',
                              name: @result.student_name,
                              default: "Kết quả thi chứng chỉ NOCN của #{@result.student_name}. " \
                                       'Xem điểm chi tiết các kỹ năng Nghe-Nói-Đọc-Viết và thông tin chứng chỉ.')
        )
      end
    end
  end
end
