class AboutController < ApplicationController
  def index
    set_breadcrumb('Giới thiệu công ty và tổ chức', about_path)

    set_meta_tags_for_page(
      title: 'Giới thiệu SEA Education - Trung tâm thi chứng chỉ NOCN uy tín',
      description: 'SEA Education là đơn vị uy tín trong lĩnh vực giáo dục, chuyên cung cấp các giải pháp ' \
                   'khảo thí và đánh giá chất lượng chứng chỉ tiếng Anh NOCN theo tiêu chuẩn quốc tế.',
      keywords: 'giới thiệu SEA Education, NOCN, chứng chỉ tiếng anh, kh<PERSON><PERSON> thí, đ<PERSON>h gi<PERSON> chất lượ<PERSON>, tiêu chuẩn quốc tế'
    )

    # Đọc nội dung từ file docx
    docx_path = 'about.docx'
    if File.exist?(docx_path)
      require 'docx'
      doc = Docx::Document.open(docx_path)
      @content = doc.paragraphs.map(&:text).join("\n")
    else
      @content = "Nội dung đang được cập nhật..."
    end

    @latest_news = [] # Add this if you have a news model/data
  end

  def certificate_introduction
    set_breadcrumb('Giới thiệu chứng chỉ NOCN', certificate_introduction_path)

    set_meta_tags_for_page(
      title: 'Giới thiệu chứng chỉ NOCN - Chứng chỉ tiếng Anh quốc tế',
      description: 'Tìm hiểu về chứng chỉ NOCN - chứng chỉ tiếng Anh được công nhận quốc tế, ' \
                   'đánh giá 4 kỹ năng Nghe-Nói-Đọc-Viết theo chuẩn CEFR. Xem mẫu chứng chỉ và thông tin chi tiết.',
      keywords: 'chứng chỉ NOCN, chứng chỉ tiếng anh quốc tế, CEFR, mẫu chứng chỉ, NOCN certificate'
    )
  end

  def exam_content_introduction
    set_breadcrumb('Giới thiệu về nội dung của bài thi', exam_content_introduction_path)

    set_meta_tags_for_page(
      title: 'Giới thiệu nội dung bài thi NOCN - Cấu trúc và kỹ năng đánh giá',
      description: 'Tìm hiểu chi tiết về nội dung các bài thi NOCN: cấu trúc đề thi, kỹ năng đánh giá, ' \
                   'thời gian làm bài và tiêu chí chấm điểm cho từng cấp độ theo chuẩn CEFR.',
      keywords: 'nội dung bài thi NOCN, cấu trúc đề thi, kỹ năng đánh giá, CEFR, thời gian làm bài'
    )

    @latest_news = [] # Add this if you have a news model/data
  end
end
