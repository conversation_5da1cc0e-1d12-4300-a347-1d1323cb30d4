class RegulationsController < ApplicationController
  def index
    set_breadcrumb('Quy định dự thi ESOL International', regulations_path)

    set_meta_tags_for_page(
      title: 'Quy định dự thi ESOL International - SEA Education',
      description: 'Tìm hiểu chi tiết về quy định dự thi ESOL International: đ<PERSON><PERSON> ký, x<PERSON><PERSON> <PERSON>h danh tính, ' \
                   'thời gian thi, quy tắc <PERSON>ng xử, thiết bị phòng thi và các quy định khác.',
      keywords: 'quy định thi ESOL, đăng ký thi, xá<PERSON> <PERSON>h danh t<PERSON>h, quy tắc thi, phòng thi, ESOL International'
    )

    @latest_news = [] # Add this if you have a news model/data
  end
end
