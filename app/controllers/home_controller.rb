class HomeController < ApplicationController
  def index
    set_meta_tags_for_page(
      title: I18n.t('home.meta.title', default: 'Trang chủ - Đăng ký thi chứng chỉ tiếng Anh NOCN'),
      description: I18n.t('home.meta.description',
                          default: 'SEA Education - Trung tâm thi chứng chỉ tiếng Anh NOCN uy tín tại Việt Nam. ' \
                                   'Đăng ký thi online, lịch thi linh ho<PERSON>, kế<PERSON> qu<PERSON> chóng, ' \
                                   'chứng chỉ được công nhận quốc tế.'),
      keywords: I18n.t('home.meta.keywords',
                       default: 'đăng ký thi NOCN, chứng chỉ tiếng anh, thi tiếng anh online, ' \
                                'SEA Education, NOCN Vietnam, lịch thi tiếng anh')
    )

    @exam_centers = Master::ExamCenter.visible
                                      .includes(exam_days: :exam_sessions)
                                      .joins(:exam_days)
                                      .where(exam_days: { date: Date.current.. })
                                      .distinct
                                      .order(:id)

    @exam_sessions = Master::ExamSession.joins(exam_day: :exam_center)
                                        .where(exam_centers: { is_visible: true })
                                        .distinct.pluck(:time_range).uniq.sort
  end
end
