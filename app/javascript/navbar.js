// Mobile Navbar Handler
document.addEventListener('DOMContentLoaded', function() {
  // Get navbar elements
  const navbarToggler = document.querySelector('.navbar-toggler');
  const navbarCollapse = document.querySelector('#navbarSupportedContent');
  const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
  const dropdownToggles = document.querySelectorAll('.navbar-nav .dropdown-toggle');

  // Handle navbar toggle
  if (navbarToggler && navbarCollapse) {
    // Remove any existing Bootstrap data attributes to prevent conflicts
    navbarToggler.removeAttribute('data-bs-toggle');
    navbarToggler.removeAttribute('data-bs-target');
    
    navbarToggler.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      const isCurrentlyOpen = navbarCollapse.classList.contains('show');
      
      if (isCurrentlyOpen) {
        closeMenu();
      } else {
        openMenu();
      }
    });

    // Functions to open/close menu
    function openMenu() {
      navbarCollapse.classList.add('show');
      navbarToggler.setAttribute('aria-expanded', 'true');
      navbarToggler.classList.remove('collapsed');
    }

    function closeMenu() {
      navbarCollapse.classList.remove('show');
      navbarToggler.setAttribute('aria-expanded', 'false');
      navbarToggler.classList.add('collapsed');
      
      // Also close any open dropdowns
      dropdownToggles.forEach(function(toggle) {
        const dropdownMenu = toggle.nextElementSibling;
        if (dropdownMenu) {
          dropdownMenu.classList.remove('show');
          toggle.setAttribute('aria-expanded', 'false');
        }
      });
    }

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
      if (!navbarToggler.contains(e.target) && !navbarCollapse.contains(e.target)) {
        if (navbarCollapse.classList.contains('show')) {
          closeMenu();
        }
      }
    });

    // Handle dropdown menus on mobile
    dropdownToggles.forEach(function(toggle) {
      // Remove Bootstrap data attributes
      toggle.removeAttribute('data-bs-toggle');
      
      toggle.addEventListener('click', function(e) {
        if (window.innerWidth < 992) {
          e.preventDefault();
          e.stopPropagation();
          
          const dropdownMenu = toggle.nextElementSibling;
          if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
            // Close other dropdowns
            dropdownToggles.forEach(function(otherToggle) {
              if (otherToggle !== toggle) {
                const otherMenu = otherToggle.nextElementSibling;
                if (otherMenu) {
                  otherMenu.classList.remove('show');
                  otherToggle.setAttribute('aria-expanded', 'false');
                }
              }
            });
            
            // Toggle current dropdown
            const isOpen = dropdownMenu.classList.contains('show');
            if (isOpen) {
              dropdownMenu.classList.remove('show');
              toggle.setAttribute('aria-expanded', 'false');
            } else {
              dropdownMenu.classList.add('show');
              toggle.setAttribute('aria-expanded', 'true');
            }
          }
        }
      });
    });

    // Close menu when clicking on nav links (mobile only)
    navLinks.forEach(function(link) {
      link.addEventListener('click', function() {
        if (window.innerWidth < 992 && !link.classList.contains('dropdown-toggle')) {
          setTimeout(function() {
            closeMenu();
          }, 150);
        }
      });
    });

    // Handle window resize
    window.addEventListener('resize', function() {
      if (window.innerWidth >= 992) {
        // Desktop: ensure menu is closed and reset mobile states
        closeMenu();
        
        // Reset dropdown states
        dropdownToggles.forEach(function(toggle) {
          const dropdownMenu = toggle.nextElementSibling;
          if (dropdownMenu) {
            dropdownMenu.classList.remove('show');
            toggle.setAttribute('aria-expanded', 'false');
          }
        });
      }
    });

    // Handle escape key to close menu
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && navbarCollapse.classList.contains('show')) {
        closeMenu();
      }
    });

    // Initialize proper state
    setTimeout(function() {
      if (!navbarToggler.classList.contains('collapsed')) {
        navbarToggler.classList.add('collapsed');
      }
      navbarToggler.setAttribute('aria-expanded', 'false');
    }, 100);
  }

  // Disable Bootstrap navbar if it's interfering
  if (typeof window.bootstrap !== 'undefined' && window.bootstrap.Collapse) {
    // Prevent Bootstrap from auto-initializing
    const bsCollapseElements = document.querySelectorAll('[data-bs-toggle="collapse"]');
    bsCollapseElements.forEach(function(element) {
      element.removeAttribute('data-bs-toggle');
      element.removeAttribute('data-bs-target');
    });
  }
}); 