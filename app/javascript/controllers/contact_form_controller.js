import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["nameError", "emailError", "messageError"]

  connect() {
    this.form = this.element
    this.messageDiv = document.getElementById('contact-message')
    this.submitButton = this.form.querySelector('input[type="submit"]')
    
    // Add real-time validation
    this.setupRealTimeValidation()
  }

  setupRealTimeValidation() {
    const nameField = this.form.querySelector('#contact_name')
    const emailField = this.form.querySelector('#contact_email')
    const messageField = this.form.querySelector('#contact_message')

    // Add input event listeners for real-time validation
    nameField?.addEventListener('blur', () => this.validateField('name', nameField.value))
    emailField?.addEventListener('blur', () => this.validateField('email', emailField.value))
    messageField?.addEventListener('blur', () => this.validateField('message', messageField.value))

    // Add typing indicators
    nameField?.addEventListener('input', () => this.handleTyping('name'))
    emailField?.addEventListener('input', () => this.handleTyping('email'))
    messageField?.addEventListener('input', () => this.handleTyping('message'))
  }

  validateField(fieldName, value) {
    const fieldElement = this.form.querySelector(`#contact_${fieldName}`)
    const errorTarget = this[`${fieldName}ErrorTarget`]
    
    let isValid = true
    let errorMessage = ''

    switch(fieldName) {
      case 'name':
        if (!value || value.trim().length < 2) {
          isValid = false
          errorMessage = 'Họ tên phải có ít nhất 2 ký tự'
        } else if (value.length > 100) {
          isValid = false
          errorMessage = 'Họ tên không được vượt quá 100 ký tự'
        }
        break
      
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!value) {
          isValid = false
          errorMessage = 'Email là bắt buộc'
        } else if (!emailRegex.test(value)) {
          isValid = false
          errorMessage = 'Email không đúng định dạng'
        } else if (value.length > 255) {
          isValid = false
          errorMessage = 'Email không được vượt quá 255 ký tự'
        }
        break
      
      case 'message':
        if (!value || value.trim().length < 10) {
          isValid = false
          errorMessage = 'Nội dung tin nhắn phải có ít nhất 10 ký tự'
        } else if (value.length > 1000) {
          isValid = false
          errorMessage = 'Nội dung tin nhắn không được vượt quá 1000 ký tự'
        }
        break
    }

    if (isValid) {
      fieldElement.classList.remove('is-invalid')
      fieldElement.classList.add('is-valid')
      errorTarget.textContent = ''
    } else {
      fieldElement.classList.remove('is-valid')
      fieldElement.classList.add('is-invalid')
      errorTarget.textContent = errorMessage
    }

    return isValid
  }

  handleTyping(fieldName) {
    const fieldElement = this.form.querySelector(`#contact_${fieldName}`)
    // Remove validation classes while typing
    fieldElement.classList.remove('is-invalid', 'is-valid')
    this[`${fieldName}ErrorTarget`].textContent = ''
  }

  submit(event) {
    event.preventDefault()
    
    // Show loading state
    this.setLoadingState(true)
    
    // Reset previous errors and messages
    this.resetErrors()
    this.hideMessage()
    
    // Validate all fields
    const nameValid = this.validateField('name', this.form.querySelector('#contact_name').value)
    const emailValid = this.validateField('email', this.form.querySelector('#contact_email').value)
    const messageValid = this.validateField('message', this.form.querySelector('#contact_message').value)
    
    if (!nameValid || !emailValid || !messageValid) {
      this.setLoadingState(false)
      this.showMessage('error', 'Vui lòng kiểm tra lại thông tin đã nhập')
      return
    }
    
    const formData = new FormData(this.form)
    
    fetch(this.form.action, {
      method: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
        'Accept': 'application/json'
      },
      body: formData
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('Network response was not ok')
      }
      return response.json()
    })
    .then(data => {
      this.setLoadingState(false)
      
      if (data.success) {
        this.showMessage('success', data.message)
        this.form.reset()
        this.resetValidationClasses()
        this.showSuccessAnimation()
      } else {
        this.showMessage('error', data.message)
        this.showErrors(data.errors)
      }
    })
    .catch(error => {
      this.setLoadingState(false)
      console.error('Error:', error)
      this.showMessage('error', 'Có lỗi xảy ra khi gửi tin nhắn. Vui lòng thử lại hoặc liên hệ trực tiếp qua điện thoại.')
    })
  }

  setLoadingState(isLoading) {
    if (isLoading) {
      this.submitButton.disabled = true
      this.submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang gửi...'
    } else {
      this.submitButton.disabled = false
      this.submitButton.innerHTML = 'Gửi tin nhắn'
    }
  }

  resetErrors() {
    this.nameErrorTarget.textContent = ''
    this.emailErrorTarget.textContent = ''
    this.messageErrorTarget.textContent = ''
    this.resetValidationClasses()
  }

  resetValidationClasses() {
    this.form.querySelectorAll('.is-invalid, .is-valid').forEach(element => {
      element.classList.remove('is-invalid', 'is-valid')
    })
  }

  showErrors(errors) {
    if (errors.name) {
      this.form.querySelector('#contact_name').classList.add('is-invalid')
      this.nameErrorTarget.textContent = errors.name.join(', ')
    }
    
    if (errors.email) {
      this.form.querySelector('#contact_email').classList.add('is-invalid')
      this.emailErrorTarget.textContent = errors.email.join(', ')
    }
    
    if (errors.message) {
      this.form.querySelector('#contact_message').classList.add('is-invalid')
      this.messageErrorTarget.textContent = errors.message.join(', ')
    }

    // Scroll to first error
    const firstError = this.form.querySelector('.is-invalid')
    if (firstError) {
      firstError.scrollIntoView({ behavior: 'smooth', block: 'center' })
      firstError.focus()
    }
  }

  showMessage(type, message) {
    this.messageDiv.className = `alert alert-${type === 'error' ? 'error' : 'success'}`
    this.messageDiv.innerHTML = `
      <div style="display: flex; align-items: center; gap: 10px;">
        <i class="fas ${type === 'error' ? 'fa-exclamation-circle' : 'fa-check-circle'}"></i>
        <span>${message}</span>
      </div>
    `
    this.messageDiv.style.display = 'block'
    
    // Smooth scroll to message
    this.messageDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
    
    // Auto hide success messages after 7 seconds
    if (type === 'success') {
      setTimeout(() => {
        this.hideMessage()
      }, 7000)
    }
  }

  hideMessage() {
    if (this.messageDiv) {
      this.messageDiv.style.display = 'none'
    }
  }

  showSuccessAnimation() {
    // Add a subtle success animation to the form
    this.form.style.transform = 'scale(1.02)'
    this.form.style.transition = 'transform 0.3s ease'
    
    setTimeout(() => {
      this.form.style.transform = 'scale(1)'
    }, 300)
  }
} 