$(document).on("ready", function () {
  $(".fileToUpload").on("click", function () {
    $(this).next().trigger("click");
  });
  $(".fileToUploadMain").on("change", function () {
    var current_nut = $(this);
    var file = $(this)[0].files[0];

    if (file) {
      var reader = new FileReader();

      reader.onload = function (e) {
        current_nut
          .parent()
          .find(".fileToUpload")
          .html('<img src="' + e.target.result + '">');
        current_nut.parent().find(".rsform-input-box").val(e.target.result);
      };

      reader.readAsDataURL(file);
    } else {
      alert("No file selected");
    }
  });
  // Sự kiện
  $(".rsform-block-donvicongtac input").on("change", function () {
    if ($(this).attr("id") == "DonViCongTac0") {
      $(".rsform-block-truongdangcongtac").css("display", "block");
      $(".rsform-block-ctydangcongtac").css("display", "none");
    } else if ($(this).attr("id") == "DonViCongTac1") {
      $(".rsform-block-truongdangcongtac").css("display", "none");
      $(".rsform-block-ctydangcongtac").css("display", "block");
    }
  });
  $(".rsform-block-gapkhokhan input").on("change", function () {
    if ($(this).is(":checked")) {
      $(".rsform-block-hotrokhokhan").css("display", "block");
    } else {
      $(".rsform-block-hotrokhokhan").css("display", "none");
    }
  });

  // Logic show/hide field dựa trên dropdown selection
  function checkDropdownSelection(blockClass, targetSelector, triggerValues) {
    const selectedValue = $(blockClass).find("select option:selected").val();
    const selectedText = $(blockClass).find(".DropDownInput input").val();
    
    // Kiểm tra cả value và text
    const shouldShow = triggerValues.some(value => 
      selectedValue === value || 
      selectedText.toLowerCase().includes(value.toLowerCase()) ||
      selectedValue === value.toLowerCase() ||
      selectedText === value
    );
    
    if (shouldShow) {
      $(targetSelector).css("display", "block");
    } else {
      $(targetSelector).css("display", "none");
    }
  }

  // Hiển thị field "Lý do khác" khi chọn "Khác" trong dropdown lý do đăng ký
  $(document).on("change", ".rsform-block-lydochonthi select", function () {
    checkDropdownSelection(".rsform-block-lydochonthi", ".rsform-block-lydochonthi .formControls.mt-2", ["Khác", "khac"]);
  });
  
  $(document).on("click", ".rsform-block-lydochonthi .DropDownList li", function () {
    setTimeout(() => {
      checkDropdownSelection(".rsform-block-lydochonthi", ".rsform-block-lydochonthi .formControls.mt-2", ["Khác", "khac"]);
    }, 100);
  });

  // Hiển thị field "Địa chỉ chuyển phát" khi chọn "Chuyển phát nhanh" 
  $(document).on("change", ".rsform-block-phuongthucnhanchungchi select", function () {
    checkDropdownSelection(".rsform-block-phuongthucnhanchungchi", ".rsform-block-phuongthucnhanchungchi .formControls.mt-2", ["Chuyển phát nhanh", "chuyen_phat_nhanh"]);
  });
  
  $(document).on("click", ".rsform-block-phuongthucnhanchungchi .DropDownList li", function () {
    setTimeout(() => {
      checkDropdownSelection(".rsform-block-phuongthucnhanchungchi", ".rsform-block-phuongthucnhanchungchi .formControls.mt-2", ["Chuyển phát nhanh", "chuyen_phat_nhanh"]);
    }, 100);
  });

  // Hiển thị field "Khó khăn khác" khi chọn "Khác" trong dropdown hỗ trợ
  $(document).on("change", ".rsform-block-hotrokhokhan select", function () {
    checkDropdownSelection(".rsform-block-hotrokhokhan", ".rsform-block-hotrokhokhan .formControls.mt-2", ["Khác", "khac"]);
  });
  
  $(document).on("click", ".rsform-block-hotrokhokhan .DropDownList li", function () {
    setTimeout(() => {
      checkDropdownSelection(".rsform-block-hotrokhokhan", ".rsform-block-hotrokhokhan .formControls.mt-2", ["Khác", "khac"]);
    }, 100);
  });
  // CLick chuột bất kỳ bên ngoài Dropdown
  $(document).on("click", function (e) {
    if ($(e.target).closest(".ListDown").length == 0) {
      $(".ListDown").removeClass("ListDown");
    }
  });
  // Dropdown select
  $(".DropDownInput input, .DropDownInput span").click(function () {
    if ($(this).parent().parent().hasClass("ListDown")) {
      $(this).parent().parent().removeClass("ListDown");
    } else {
      $(".ListDown").removeClass("ListDown");
      $(this).parent().parent().addClass("ListDown");
    }
  });
  function ChangeInputSearch(current_input) {
    if (current_input.val() != "") {
      var current_val = current_input.val().toLowerCase();
      current_input
        .parent()
        .parent()
        .find("li")
        .each(function () {
          if ($.trim($(this).text()).toLowerCase().indexOf(current_val) > -1) {
            $(this).css("display", "block");
          } else {
            $(this).css("display", "none");
          }
        });
    } else {
      current_input.parent().parent().find("li").css("display", "block");
    }
  }
  $(".DropDownList input").on("keyup", function () {
    ChangeInputSearch($(this));
  });
  $(".DropDownList input").on("change", function () {
    ChangeInputSearch($(this));
  });
  $(".DropDownListClose span").on("click", function () {
    $(this).parent().parent().parent().removeClass("ListDown");
  });

  function khongdau(str) {
    str = str.toLowerCase();
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.toUpperCase();
    return str;
  }

  $("#Ho, #Ten").on("keyup change", function (e) {
    this.value = khongdau(this.value);
  });
});
