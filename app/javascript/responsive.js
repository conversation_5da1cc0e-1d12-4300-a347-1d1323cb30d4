$(document).on("ready", function () {
  $("#Mod377 .workshome-content >p").on("click", function () {
    $("#Mod377 .workshome-content >form").toggle();
  });
  $(".module_search")
    .parent()
    .on("keyup keypress", function (e) {
      var key = e.key || e.which;
      if (key === "Enter" || key === 13) {
        e.preventDefault();
        $(this).find(".buttontim").trigger("click");
      }
    });
  $(".buttontim").on("click", function () {
    var form = ".searchID" + $(this).attr("data-searchid");
    $(form)
      .find(".selectID")
      .each(function () {
        if ($(this).val() == "0") {
          $(this).attr("disabled", true);
        }
      });
    $(form).trigger("submit");
  });

  var ten = [];
  var menu = [];
  ten[0] = "Menu";
  menu[0] = $("#ja-menungang");
  ten[1] = "Sản phẩm";
  menu[1] = $("#Mod326");
  var htmlmenu = "";
  var icon = "";
  for (var i = 0; i < menu.length; i++) {
    if (i == 0) {
      icon = '<i class="fa fa-bars"></i>';
    }
    if (i == 1) {
      icon = '<i class="fa fa-list-ul"></i>';
    }
    htmlmenu =
      htmlmenu +
      '<div class="mobile_' +
      i +
      '"><div class="tieude">' +
      icon +
      ten[i] +
      '</div><div class="noidung"><ul>' +
      menu[i].find(".menu").html() +
      "</ul></div></div>";
  }
  htmlmenu =
    '<div class="mobile_menu">' + htmlmenu + '<div class="nentat"></div></div>';
  $("#ja-menungang").after(htmlmenu);

  $(".mobile_menu .nentat").on("click", function () {
    $(".mnmb").removeClass("mnmb");
  });
  $(".mobile_menu .tieude").on("click", function () {
    $(this).next().addClass("mnmb");
    $(".nentat").addClass("mnmb");
  });
  if (jQuery("#ja-menungang").css("background-color") == "rgba(0, 0, 0, 0)") {
    $(".mobile_menu .tieude").css("background-color", "transparent");
  } else {
    $(".mobile_menu .tieude").css("background-color", "transparent");
  }

  $(".mobile_menu li.parent>a, .mobile_menu li.parent>span.separator").after(
    '<span class="add">+</span>'
  );
  $(".mobile_menu li.parent >span ").on("click", function () {
    $(this).hasClass("add") === true ? $(this).text("-") : $(this).text("+");
    $(this).removeClass("sub");
    $(this).addClass("add");
    $(this).next().slideUp();

    if (!$(this).next().is(":visible")) {
      $(this).addClass("sub");
      $(this).removeClass("add");
      $(this).next().slideDown();
    }
  });
});
