# Pin npm packages by running ./bin/importmap

pin 'application'
pin 'popper', to: 'popper.js', preload: true
pin 'bootstrap', to: 'bootstrap.min.js', preload: true
pin 'jquery', to: 'jquery.min.js', preload: true
pin 'jquery_ujs', to: 'jquery_ujs.js', preload: true
pin '@hotwired/stimulus', to: 'stimulus.min.js'
pin '@hotwired/stimulus-loading', to: 'stimulus-loading.js'
pin_all_from 'app/javascript/controllers', under: 'controllers'
pin 'select2-full', to: 'select2-full.js', preload: true
pin 'responsive', to: 'responsive.js'
pin 'rs_form', to: 'rs_form.js'
pin 'navbar', to: 'navbar.js'
