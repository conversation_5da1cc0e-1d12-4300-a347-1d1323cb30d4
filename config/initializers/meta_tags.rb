# frozen_string_literal: true

MetaTags.configure do |config|
  # How many characters should the title meta tag have at most. Default is 70.
  # Set to nil or 0 to remove limits.
  config.title_limit = 70

  # When true, site title will be truncated instead of title. Default is false.
  config.truncate_site_title_first = false

  # How many characters should the description meta tag have at most. Default is 300.
  # Set to nil or 0 to remove limits.
  config.description_limit = 300

  # How many characters should the keywords meta tag have at most. Default is 255.
  config.keywords_limit = 255

  # Should keywords be converted to lowercase? Default is true.
  config.keywords_lowercase = true

  # When true, keywords will be separated with a comma. Default is true.
  config.keywords_separator = ', '

  # When true, the output will not include new line characters between tags. Default is false.
  config.minify_output = false

  # When false, generated meta tags will be self-closing (<tag ... />) instead of open (<tag ...>). Default is true.
  config.open_meta_tags = true

  # List of additional meta tags that should use "property" attribute instead of "name" attribute.
  config.property_tags.push(
    'article:author', 'article:publisher', 'article:tag', 'article:section',
    'fb:admins', 'fb:app_id',
    'music:album', 'music:musician', 'music:song',
    'place:location:latitude', 'place:location:longitude',
    'product:price:amount', 'product:price:currency',
    'video:actor', 'video:director', 'video:writer'
  )
end
